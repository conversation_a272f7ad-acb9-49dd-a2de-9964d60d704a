// Centralized mock exports for node application tests

// Database/Sequelize mocks
const mockSequelize = {
  authenticate: jest.fn(),
  sync: jest.fn(),
  close: jest.fn(),
  transaction: jest.fn(),
  query: jest.fn(),
  define: jest.fn()
};

const mockModel = {
  findAll: jest.fn(),
  findOne: jest.fn(),
  findByPk: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  destroy: jest.fn(),
  count: jest.fn(),
  findAndCountAll: jest.fn(),
  bulkCreate: jest.fn()
};

// AWS Cognito client mock
const mockCognitoClient = {
  adminCreateUser: jest.fn(),
  adminSetUserPassword: jest.fn(),
  adminDeleteUser: jest.fn(),
  adminGetUser: jest.fn(),
  adminUpdateUserAttributes: jest.fn(),
  adminInitiateAuth: jest.fn(),
  forgotPassword: jest.fn(),
  confirmForgotPassword: jest.fn(),
  globalSignOut: jest.fn()
};

// AWS S3 client mock
const mockS3Client = {
  upload: jest.fn(),
  getObject: jest.fn(),
  deleteObject: jest.fn(),
  putObject: jest.fn(),
  headObject: jest.fn()
};

// Chargebee client mock
const mockChargebeeClient = {
  customer: {
    create: jest.fn(),
    retrieve: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    list: jest.fn()
  },
  subscription: {
    create: jest.fn(),
    retrieve: jest.fn(),
    update: jest.fn(),
    cancel: jest.fn(),
    list: jest.fn(),
    estimate: jest.fn()
  },
  card: {
    create: jest.fn(),
    retrieve: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    list: jest.fn()
  },
  invoice: {
    list: jest.fn(),
    retrieve: jest.fn(),
    pdf: jest.fn()
  },
  payment_method: {
    create: jest.fn(),
    retrieve: jest.fn(),
    update: jest.fn(),
    delete: jest.fn()
  }
};

// Salesforce client mock
const mockSalesforceClient = {
  login: jest.fn(),
  query: jest.fn(),
  sobject: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn()
};

// Elasticsearch client mock
const mockElasticsearchClient = {
  index: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  search: jest.fn(),
  indices: {
    create: jest.fn(),
    exists: jest.fn(),
    delete: jest.fn()
  }
};

// Logger mock
const mockLogger = {
  log: jest.fn(),
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
};

// Axios mock
const mockAxios = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  create: jest.fn(() => mockAxios)
};

// Express mock
const mockExpress = {
  Router: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    use: jest.fn()
  })),
  json: jest.fn(),
  urlencoded: jest.fn(),
  static: jest.fn()
};

// Multer mock
const mockMulter = {
  single: jest.fn(() => (req, res, next) => next()),
  array: jest.fn(() => (req, res, next) => next()),
  fields: jest.fn(() => (req, res, next) => next())
};

// Mock factory functions
const createMockService = (methods = {}) => {
  const defaultMethods = {
    getCustomerDetails: jest.fn(),
    updateCustomerDetails: jest.fn(),
    resetPassword: jest.fn(),
    logout: jest.fn(),
    managePlan: jest.fn(),
    getPlanDetails: jest.fn(),
    addCard: jest.fn(),
    updateCard: jest.fn(),
    deleteCard: jest.fn(),
    fetchCard: jest.fn()
  };
  
  return {
    ...defaultMethods,
    ...methods
  };
};

const createMockController = (methods = {}) => {
  const defaultMethods = {
    getCustomerDetails: jest.fn(),
    updateCustomerDetails: jest.fn(),
    resetPassword: jest.fn(),
    logout: jest.fn(),
    userLogin: jest.fn(),
    userSignupQueue: jest.fn(),
    userForgotPassword: jest.fn(),
    userResetPassword: jest.fn()
  };
  
  return {
    ...defaultMethods,
    ...methods
  };
};

// Mock middleware
const mockMiddleware = {
  authenticateToken: jest.fn((req, res, next) => {
    req.userDetails = testUtils.createMockCustomer();
    req.elasticLogObj = { requestId: 'test-request-id' };
    next();
  }),
  authenticateRegistrationToken: jest.fn((req, res, next) => next()),
  authenticateWebhook: jest.fn((req, res, next) => next()),
  validator: jest.fn(() => (req, res, next) => next()),
  rateLimiter: jest.fn((req, res, next) => next()),
  decodeBodyPayload: jest.fn((req, res, next) => next())
};

// Mock responses
const mockResponses = {
  customer: {
    id: 'test-customer-id',
    firstName: 'Test',
    lastName: 'Customer',
    email: '<EMAIL>',
    phone: '555-1234',
    status: 'active'
  },
  
  subscription: {
    id: 'test-subscription-id',
    customerId: 'test-customer-id',
    planId: 'test-plan-id',
    status: 'active',
    currentTermStart: new Date().toISOString(),
    currentTermEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
  },
  
  card: {
    id: 'test-card-id',
    customerId: 'test-customer-id',
    last4: '1234',
    brand: 'visa',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true
  },
  
  plan: {
    id: 'test-plan-id',
    name: 'Test Plan',
    price: 29.99,
    currency: 'USD',
    period: 'month',
    features: ['Feature 1', 'Feature 2']
  },
  
  invoice: {
    id: 'test-invoice-id',
    customerId: 'test-customer-id',
    subscriptionId: 'test-subscription-id',
    total: 29.99,
    status: 'paid',
    date: new Date().toISOString()
  },
  
  cognitoUser: {
    User: {
      Username: 'test-user-id',
      Attributes: [
        { Name: 'email', Value: '<EMAIL>' },
        { Name: 'given_name', Value: 'Test' },
        { Name: 'family_name', Value: 'User' }
      ],
      UserStatus: 'CONFIRMED',
      Enabled: true
    }
  },
  
  elasticResponse: {
    _id: 'test-elastic-id',
    _index: 'test-index',
    _version: 1,
    result: 'created'
  },
  
  apiSuccess: {
    status: 200,
    message: 'Success',
    data: {}
  },
  
  apiError: {
    status: 400,
    message: 'Bad Request',
    error: 'Something went wrong'
  }
};

module.exports = {
  mockSequelize,
  mockModel,
  mockCognitoClient,
  mockS3Client,
  mockChargebeeClient,
  mockSalesforceClient,
  mockElasticsearchClient,
  mockLogger,
  mockAxios,
  mockExpress,
  mockMulter,
  createMockService,
  createMockController,
  mockMiddleware,
  mockResponses
};
