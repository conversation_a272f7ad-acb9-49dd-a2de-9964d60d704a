const request = require('supertest');
const express = require('express');
const { mockCognitoClient, mockChargebeeClient, mockModel, mockResponses, mockMiddleware } = require('../mocks');

// Mock all dependencies before requiring the app
jest.mock('../../clients/cognito/cognito-client');
jest.mock('../../clients/chargebee/chargebee-client');
jest.mock('../../models');
jest.mock('../../middleware/userAuth');
jest.mock('../../middleware/validationMid');
jest.mock('../../utils/logger');
jest.mock('../../helper/custom-helper');

const CognitoClient = require('../../clients/cognito/cognito-client');
const ChargebeeClient = require('../../clients/chargebee/chargebee-client');
const { Customer, Subscription, Card } = require('../../models');
const { authenticateToken, authenticateRegistrationTokenV1 } = require('../../middleware/userAuth');
const { validator } = require('../../middleware/validationMid');
const { getCurrentAtlanticTime } = require('../../helper/custom-helper');

describe('Node Application Integration Tests', () => {
  let app;

  beforeEach(() => {
    // Mock all clients
    CognitoClient.mockImplementation(() => mockCognitoClient);
    ChargebeeClient.mockImplementation(() => mockChargebeeClient);
    
    // Mock all models
    Customer.mockImplementation(() => mockModel);
    Subscription.mockImplementation(() => mockModel);
    Card.mockImplementation(() => mockModel);

    // Mock middleware
    authenticateToken.mockImplementation(mockMiddleware.authenticateToken);
    authenticateRegistrationTokenV1.mockImplementation(mockMiddleware.authenticateRegistrationToken);
    validator.mockImplementation(() => mockMiddleware.validator());

    // Mock helper functions
    getCurrentAtlanticTime.mockReturnValue(new Date().toISOString());

    // Create a fresh app instance for each test
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Add routes
    require('../../routes/auth-route')(app);
    require('../../routes/customer-route')(app);
    require('../../routes/card-route')(app);
    require('../../routes/subscription-route')(app);
    
    // Add error handling
    app.use((err, req, res, next) => {
      res.status(500).json({ 
        status: 500,
        message: 'Internal Server Error',
        error: err.message 
      });
    });
  });

  describe('Authentication Flow Integration', () => {
    it('should handle complete login flow', async () => {
      // Mock successful Cognito authentication
      mockCognitoClient.adminInitiateAuth.mockResolvedValue({
        AuthenticationResult: {
          AccessToken: 'test-access-token',
          RefreshToken: 'test-refresh-token',
          IdToken: 'test-id-token'
        }
      });

      mockCognitoClient.adminGetUser.mockResolvedValue(mockResponses.cognitoUser);
      mockModel.findOne.mockResolvedValue({
        dataValues: mockResponses.customer,
        get: jest.fn((key) => mockResponses.customer[key])
      });

      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: {
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          user: mockResponses.customer
        }
      });
    });

    it('should handle registration flow', async () => {
      mockCognitoClient.adminCreateUser.mockResolvedValue({
        User: {
          Username: 'test-user-id',
          UserStatus: 'FORCE_CHANGE_PASSWORD'
        }
      });

      mockModel.create.mockResolvedValue({
        dataValues: mockResponses.customer,
        get: jest.fn((key) => mockResponses.customer[key])
      });

      const response = await request(app)
        .post('/auth/register')
        .send({
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          phone: '555-1234',
          password: 'password123'
        })
        .expect(200);

      expect(response.body.status).toBe(200);
      expect(response.body.message).toBe('Success');
    });

    it('should handle forgot password flow', async () => {
      mockCognitoClient.forgotPassword.mockResolvedValue({
        CodeDeliveryDetails: {
          Destination: 't***@example.com',
          DeliveryMedium: 'EMAIL'
        }
      });

      const response = await request(app)
        .post('/auth/forgot-password')
        .send({
          email: '<EMAIL>'
        })
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: { message: 'Password reset code sent to email' }
      });
    });

    it('should handle password reset flow', async () => {
      mockCognitoClient.confirmForgotPassword.mockResolvedValue({});

      const response = await request(app)
        .post('/auth/reset-password')
        .send({
          email: '<EMAIL>',
          confirmationCode: '123456',
          newPassword: 'newpassword123'
        })
        .expect(200);

      expect(response.body.status).toBe(200);
      expect(response.body.message).toBe('Success');
    });
  });

  describe('Customer Management Integration', () => {
    it('should handle complete customer details flow', async () => {
      mockModel.findOne.mockResolvedValue({
        dataValues: mockResponses.customer,
        get: jest.fn((key) => mockResponses.customer[key])
      });

      mockModel.findAll.mockResolvedValue([
        {
          dataValues: mockResponses.subscription,
          get: jest.fn((key) => mockResponses.subscription[key])
        }
      ]);

      const response = await request(app)
        .get('/api/v1/customer/details')
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: {
          customer: mockResponses.customer,
          subscriptions: [mockResponses.subscription]
        }
      });
    });

    it('should handle customer update flow', async () => {
      const mockCustomer = {
        dataValues: mockResponses.customer,
        get: jest.fn((key) => mockResponses.customer[key]),
        update: jest.fn().mockResolvedValue(true)
      };

      mockModel.findOne.mockResolvedValue(mockCustomer);
      mockCognitoClient.adminUpdateUserAttributes.mockResolvedValue({});

      const updateData = {
        firstName: 'Updated',
        lastName: 'User',
        phone: '555-5678'
      };

      const response = await request(app)
        .put('/api/v1/customer')
        .send(updateData)
        .expect(200);

      expect(response.body.status).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(mockCustomer.update).toHaveBeenCalledWith(updateData);
    });

    it('should handle password reset flow', async () => {
      mockCognitoClient.adminSetUserPassword.mockResolvedValue({});

      const response = await request(app)
        .post('/api/v1/customer/reset-password')
        .send({
          oldPassword: 'oldpassword123',
          newPassword: 'newpassword123'
        })
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: { message: 'Password reset successfully' }
      });
    });
  });

  describe('Card Management Integration', () => {
    it('should handle add card flow', async () => {
      mockChargebeeClient.card.create.mockResolvedValue({
        card: mockResponses.card
      });

      mockModel.create.mockResolvedValue({
        dataValues: mockResponses.card,
        get: jest.fn((key) => mockResponses.card[key])
      });

      const cardData = {
        number: '****************',
        expiryMonth: 12,
        expiryYear: 2025,
        cvv: '123',
        holderName: 'Test User'
      };

      const response = await request(app)
        .post('/api/v1/card')
        .send(cardData)
        .expect(200);

      expect(response.body.status).toBe(200);
      expect(response.body.message).toBe('Success');
    });

    it('should handle fetch cards flow', async () => {
      mockModel.findAll.mockResolvedValue([
        {
          dataValues: mockResponses.card,
          get: jest.fn((key) => mockResponses.card[key])
        }
      ]);

      const response = await request(app)
        .get('/api/v1/card')
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: [mockResponses.card]
      });
    });

    it('should handle delete card flow', async () => {
      const mockCard = {
        dataValues: mockResponses.card,
        get: jest.fn((key) => mockResponses.card[key]),
        destroy: jest.fn().mockResolvedValue(true)
      };

      mockModel.findOne.mockResolvedValue(mockCard);
      mockChargebeeClient.card.delete.mockResolvedValue({});

      const response = await request(app)
        .delete('/api/v1/card/test-card-id')
        .expect(200);

      expect(response.body.status).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(mockCard.destroy).toHaveBeenCalled();
    });
  });

  describe('Subscription Management Integration', () => {
    it('should handle renewal estimate flow', async () => {
      mockChargebeeClient.subscription.estimate.mockResolvedValue({
        estimate: {
          nextBillingAt: Math.floor(Date.now() / 1000) + 86400,
          invoiceEstimate: {
            total: 9999,
            currency: 'USD',
            lineItems: [
              { description: 'Internet Plan', amount: 5999 },
              { description: 'TV Package', amount: 3999 }
            ]
          }
        }
      });

      const response = await request(app)
        .get('/api/v1/subscription/renewal-estimate')
        .query({ subscriptionId: 'test-subscription-id' })
        .expect(200);

      expect(response.body.status).toBe(200);
      expect(response.body.message).toBe('Success');
    });

    it('should handle subscription update flow', async () => {
      mockChargebeeClient.subscription.update.mockResolvedValue({
        subscription: {
          ...mockResponses.subscription,
          planId: 'new-plan-id'
        }
      });

      mockModel.findOne.mockResolvedValue({
        dataValues: mockResponses.subscription,
        get: jest.fn((key) => mockResponses.subscription[key]),
        update: jest.fn().mockResolvedValue(true)
      });

      const response = await request(app)
        .put('/api/v1/subscription/internet')
        .send({
          subscriptionId: 'test-subscription-id',
          newPlanId: 'new-plan-id'
        })
        .expect(200);

      expect(response.body.status).toBe(200);
      expect(response.body.message).toBe('Success');
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle authentication errors', async () => {
      const error = new Error('Invalid credentials');
      error.code = 'NotAuthorizedException';
      mockCognitoClient.adminInitiateAuth.mockRejectedValue(error);

      await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(400);
    });

    it('should handle service unavailable errors', async () => {
      const error = new Error('Service unavailable');
      mockCognitoClient.adminInitiateAuth.mockRejectedValue(error);

      await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(500);
    });

    it('should handle validation errors', async () => {
      await request(app)
        .post('/auth/login')
        .send({
          email: 'invalid-email'
          // Missing password
        })
        .expect(500);
    });
  });

  describe('Performance and Monitoring', () => {
    it('should handle concurrent requests', async () => {
      mockModel.findOne.mockResolvedValue({
        dataValues: mockResponses.customer,
        get: jest.fn((key) => mockResponses.customer[key])
      });

      const requests = Array(10).fill().map(() => 
        request(app).get('/api/v1/customer/details')
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.message).toBe('Success');
      });
    });

    it('should track request processing time', async () => {
      const startTime = Date.now();
      
      mockModel.findOne.mockImplementation(async () => {
        await testUtils.wait(50); // Simulate processing time
        return {
          dataValues: mockResponses.customer,
          get: jest.fn((key) => mockResponses.customer[key])
        };
      });

      await request(app)
        .get('/api/v1/customer/details')
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeGreaterThan(50);
    });
  });
});
