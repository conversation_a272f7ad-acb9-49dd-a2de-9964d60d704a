const { decryptXML } = require("../helper/custom-helper");
const WebhookApiService = require("../services/webhook-api-services");
const webhookApiService = new WebhookApiService();
const CONFIG = require('../config');
const EnhancedLogger = require("../utils/enhanced-logger");

class WebhookController {
    constructor() {
        this.logger = new EnhancedLogger();
    }

    async updateWebhook(req, res, next) {
        const timer = this.logger.createTimer('webhook_processing');
        let verified = false;
        let webhookData = null;
        let sfApiName = null;
        let organizationId = null;

        try {
            // Log incoming webhook request
            await this.logger.info('Webhook request received', {
                contentType: req.get('Content-Type'),
                contentLength: req.get('Content-Length'),
                userAgent: req.get('User-Agent'),
                ip: req.ip,
                requestId: req.logData?.requestId
            }, 'webhook_received_logs');

            // Parse XML and decrypt
            const xmlParseTimer = this.logger.createTimer('xml_parsing');
            const { status, webhookData: parsedData, sfApiName: apiName, organizationId: orgId, sessionId } = await decryptXML(req.body, "order");
            await xmlParseTimer.end({
                success: status,
                sfApiName: apiName,
                organizationId: orgId
            });

            webhookData = parsedData;
            sfApiName = apiName;
            organizationId = orgId;

            await this.logger.logXmlParsing(status, req.get('Content-Length'), sfApiName, {
                organizationId,
                sessionId,
                requestId: req.logData?.requestId
            });

            // Verify organization
            let first15 = organizationId?.substring(0, 15);
            verified = first15 === CONFIG.ORGANIZATIONID;

            await this.logger.logAuthentication(verified, organizationId, {
                sfApiName,
                sessionId,
                requestId: req.logData?.requestId
            });

            if (status && webhookData && sfApiName && verified) {
                // Store webhook data in request for middleware logging
                req.webhookData = webhookData;
                req.sfApiName = sfApiName;
                req.organizationId = organizationId;

                // Process webhook data
                const processingTimer = this.logger.createTimer('webhook_data_processing');
                await webhookApiService.checkAndUpdateWebhookData(webhookData, sfApiName, "update", organizationId, sessionId);
                await processingTimer.end({
                    success: true,
                    sfApiName,
                    recordId: webhookData?.Id
                });

                await this.logger.logWebhookProcessing(sfApiName, webhookData, 'completed', {
                    organizationId,
                    sessionId,
                    requestId: req.logData?.requestId
                });
            } else {
                await this.logger.warn('Webhook processing skipped', {
                    status,
                    hasWebhookData: !!webhookData,
                    sfApiName,
                    verified,
                    organizationId,
                    requestId: req.logData?.requestId
                }, 'webhook_processing_logs');
            }

            await timer.end({
                success: true,
                verified,
                sfApiName,
                organizationId,
                recordId: webhookData?.Id
            });

        } catch (error) {
            await timer.end({
                success: false,
                error: error.message,
                sfApiName,
                organizationId
            });

            await this.logger.logWebhookError(sfApiName, error, webhookData, {
                organizationId,
                verified,
                requestId: req.logData?.requestId
            });

        } finally {
            // Log response
            await this.logger.info('Webhook response sent', {
                verified,
                sfApiName,
                organizationId,
                responseStatus: 200,
                requestId: req.logData?.requestId
            }, 'webhook_response_logs');

            res.set('Content-Type', 'application/xml');
            res.status(200).send(`
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                <soap:Body>
                    <response>
                        <status>${verified}</status>
                    </response>
                </soap:Body>
            </soap:Envelope>
            `);
        }
    }
}

module.exports = new WebhookController();