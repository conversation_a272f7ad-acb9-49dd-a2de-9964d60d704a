const request = require('supertest');
const express = require('express');
const WebhookController = require('../../controllers/webhook-controller');
const { mockEnhancedLogger, mockHelpers, mockResponses, sampleXmlData, createMockWebhookService } = require('../mocks');

// Mock dependencies
jest.mock('../../utils/enhanced-logger');
jest.mock('../../helper/custom-helper');
jest.mock('../../services/webhook-api-services');
jest.mock('../../config', () => ({
  ORGANIZATIONID: '00DSv000000nGA5',
  NODE_ENV: 'test'
}));

const { decryptXML } = require('../../helper/custom-helper');
const WebhookApiService = require('../../services/webhook-api-services');

describe('WebhookController', () => {
  let app;
  let mockWebhookService;

  beforeEach(() => {
    // Setup Express app for testing
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Add mock request logging data
    app.use((req, res, next) => {
      req.logData = { requestId: 'webhook-test-request-id', startTime: Date.now() };
      next();
    });

    // Setup routes
    app.post('/webhook/upsert', WebhookController.updateWebhook.bind(WebhookController));

    // Setup service mocks
    mockWebhookService = createMockWebhookService({
      checkAndUpdateWebhookData: jest.fn()
    });
    WebhookApiService.mockImplementation(() => mockWebhookService);

    // Setup helper mocks
    decryptXML.mockImplementation(mockHelpers.decryptXML);
  });

  describe('POST /webhook/upsert', () => {
    it('should successfully process valid webhook data', async () => {
      const mockDecryptedData = {
        ...mockResponses.decryptedXml,
        organizationId: '00DSv000000nGA5MAM'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);
      mockWebhookService.checkAndUpdateWebhookData.mockResolvedValue({ success: true });

      const response = await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>true</status>');
      expect(response.headers['content-type']).toContain('application/xml');
      
      expect(decryptXML).toHaveBeenCalledWith(sampleXmlData.contact, 'order');
      expect(mockWebhookService.checkAndUpdateWebhookData).toHaveBeenCalledWith(
        mockDecryptedData.webhookData,
        mockDecryptedData.sfApiName,
        'update',
        mockDecryptedData.organizationId,
        mockDecryptedData.sessionId
      );
    });

    it('should reject webhook with invalid organization ID', async () => {
      const mockDecryptedData = {
        ...mockResponses.decryptedXml,
        organizationId: 'INVALID_ORG_ID'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);

      const response = await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>false</status>');
      expect(mockWebhookService.checkAndUpdateWebhookData).not.toHaveBeenCalled();
    });

    it('should handle XML parsing failures', async () => {
      decryptXML.mockResolvedValue({
        status: false,
        webhookData: null,
        sfApiName: null,
        organizationId: null,
        sessionId: null
      });

      const response = await request(app)
        .post('/webhook/upsert')
        .send('invalid xml data')
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>false</status>');
      expect(mockWebhookService.checkAndUpdateWebhookData).not.toHaveBeenCalled();
    });

    it('should handle service processing errors gracefully', async () => {
      const mockDecryptedData = {
        ...mockResponses.decryptedXml,
        organizationId: '00DSv000000nGA5MAM'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);
      mockWebhookService.checkAndUpdateWebhookData.mockRejectedValue(new Error('Service error'));

      const response = await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      // Should still return XML response even with processing errors
      expect(response.text).toContain('<status>true</status>'); // Auth was successful
      expect(response.headers['content-type']).toContain('application/xml');
    });

    it('should log webhook received events', async () => {
      const mockDecryptedData = {
        ...mockResponses.decryptedXml,
        organizationId: '00DSv000000nGA5MAM'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);
      mockWebhookService.checkAndUpdateWebhookData.mockResolvedValue({ success: true });

      await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(mockEnhancedLogger.prototype.info).toHaveBeenCalledWith(
        'Webhook request received',
        expect.objectContaining({
          contentType: 'application/xml',
          requestId: 'webhook-test-request-id'
        }),
        'webhook_received_logs'
      );
    });

    it('should log XML parsing results', async () => {
      const mockDecryptedData = {
        ...mockResponses.decryptedXml,
        organizationId: '00DSv000000nGA5MAM'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);

      await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(mockEnhancedLogger.prototype.logXmlParsing).toHaveBeenCalledWith(
        true,
        undefined, // Content-Length not set in test
        'Contact',
        expect.objectContaining({
          organizationId: '00DSv000000nGA5MAM',
          requestId: 'webhook-test-request-id'
        })
      );
    });

    it('should log authentication results', async () => {
      const mockDecryptedData = {
        ...mockResponses.decryptedXml,
        organizationId: '00DSv000000nGA5MAM'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);

      await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(mockEnhancedLogger.prototype.logAuthentication).toHaveBeenCalledWith(
        true,
        '00DSv000000nGA5MAM',
        expect.objectContaining({
          sfApiName: 'Contact',
          requestId: 'webhook-test-request-id'
        })
      );
    });

    it('should log webhook processing completion', async () => {
      const mockDecryptedData = {
        ...mockResponses.decryptedXml,
        organizationId: '00DSv000000nGA5MAM'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);
      mockWebhookService.checkAndUpdateWebhookData.mockResolvedValue({ success: true });

      await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(mockEnhancedLogger.prototype.logWebhookProcessing).toHaveBeenCalledWith(
        'Contact',
        mockDecryptedData.webhookData,
        'completed',
        expect.objectContaining({
          organizationId: '00DSv000000nGA5MAM',
          requestId: 'webhook-test-request-id'
        })
      );
    });

    it('should create and end performance timers', async () => {
      const mockTimer = { end: jest.fn() };
      mockEnhancedLogger.prototype.createTimer.mockReturnValue(mockTimer);
      
      const mockDecryptedData = {
        ...mockResponses.decryptedXml,
        organizationId: '00DSv000000nGA5MAM'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);
      mockWebhookService.checkAndUpdateWebhookData.mockResolvedValue({ success: true });

      await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(mockEnhancedLogger.prototype.createTimer).toHaveBeenCalledWith('webhook_processing');
      expect(mockTimer.end).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        verified: true,
        sfApiName: 'Contact'
      }));
    });
  });

  describe('Different Salesforce Object Types', () => {
    it('should handle Customer_Details__c webhook', async () => {
      const mockDecryptedData = {
        status: true,
        webhookData: { Id: 'test-customer-id', Name: 'Test Customer' },
        sfApiName: 'Customer_Details__c',
        organizationId: '00DSv000000nGA5MAM',
        sessionId: 'test-session'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);
      mockWebhookService.checkAndUpdateWebhookData.mockResolvedValue({ success: true });

      const response = await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.customerDetails)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>true</status>');
      expect(mockWebhookService.checkAndUpdateWebhookData).toHaveBeenCalledWith(
        mockDecryptedData.webhookData,
        'Customer_Details__c',
        'update',
        mockDecryptedData.organizationId,
        mockDecryptedData.sessionId
      );
    });

    it('should handle Service_Address__c webhook', async () => {
      const mockDecryptedData = {
        status: true,
        webhookData: { Id: 'test-address-id', Name: 'Test Address' },
        sfApiName: 'Service_Address__c',
        organizationId: '00DSv000000nGA5MAM',
        sessionId: 'test-session'
      };
      
      decryptXML.mockResolvedValue(mockDecryptedData);
      mockWebhookService.checkAndUpdateWebhookData.mockResolvedValue({ success: true });

      await request(app)
        .post('/webhook/upsert')
        .send('<xml>mock address data</xml>')
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(mockWebhookService.checkAndUpdateWebhookData).toHaveBeenCalledWith(
        mockDecryptedData.webhookData,
        'Service_Address__c',
        'update',
        mockDecryptedData.organizationId,
        mockDecryptedData.sessionId
      );
    });
  });

  describe('Error Scenarios', () => {
    it('should handle decryptXML throwing an error', async () => {
      decryptXML.mockRejectedValue(new Error('XML parsing failed'));

      const response = await request(app)
        .post('/webhook/upsert')
        .send('malformed xml')
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>false</status>');
      expect(mockEnhancedLogger.prototype.logWebhookError).toHaveBeenCalled();
    });

    it('should log errors with proper context', async () => {
      const error = new Error('Processing failed');
      decryptXML.mockRejectedValue(error);

      await request(app)
        .post('/webhook/upsert')
        .send('invalid xml')
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(mockEnhancedLogger.prototype.logWebhookError).toHaveBeenCalledWith(
        null, // sfApiName is null due to parsing failure
        error,
        null, // webhookData is null due to parsing failure
        expect.objectContaining({
          verified: false,
          requestId: 'webhook-test-request-id'
        })
      );
    });
  });
});
