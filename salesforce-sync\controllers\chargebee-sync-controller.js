
const InvoicesSyncService = require("../services/chargebee/invoice-sync-services")
const invoicesSyncService = new InvoicesSyncService();
const CronService = require("../services/cron-services")
const cronService = new CronService();
const EnhancedLogger = require("../utils/enhanced-logger");

class ChargebeeSyncController {
    constructor() {
        this.logger = new EnhancedLogger();
    }

    async syncInvoicesFromChargebee(req, res, next) {
        const timer = this.logger.createTimer('chargebee_invoice_sync_operation');

        try {
            await this.logger.info('Chargebee invoice sync request received', {
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            const result = await invoicesSyncService.getInvoicesList();

            await timer.end({
                success: true,
                invoicesProcessed: result?.invoiceCount || 0,
                requestId: req.logData?.requestId
            });

            await this.logger.info('Chargebee invoice sync completed', {
                result,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            res.status(200).send({
                message: `Success`,
                invoicesProcessed: result?.invoiceCount || 0
            });
        } catch (error) {
            await timer.end({
                success: false,
                error: error.message,
                requestId: req.logData?.requestId
            });

            await this.logger.error('Chargebee invoice sync failed', {
                error: error.message,
                stack: error.stack,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            next(error);
        }
    }

    async removeServicesFromDatabase(req, res, next) {
        const timer = this.logger.createTimer('remove_services_operation');

        try {
            await this.logger.info('Remove services request received', {
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            const result = await cronService.updateSpeedchange();
            // await cronService.updateTvDate();
            // await cronService.updatePhoneDate();

            await timer.end({
                success: true,
                servicesRemoved: result?.removedCount || 0,
                requestId: req.logData?.requestId
            });

            await this.logger.info('Remove services completed', {
                result,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            res.status(200).send({
                message: `Success`,
                servicesRemoved: result?.removedCount || 0
            });
        } catch (error) {
            await timer.end({
                success: false,
                error: error.message,
                requestId: req.logData?.requestId
            });

            await this.logger.error('Remove services failed', {
                error: error.message,
                stack: error.stack,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            next(error);
        }
    }
}

module.exports = new ChargebeeSyncController();