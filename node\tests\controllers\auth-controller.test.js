const request = require('supertest');
const express = require('express');
const AuthController = require('../../controllers/auth-controller');
const { mockCognitoClient, mockResponses, createMockService, mockMiddleware } = require('../mocks');

// Mock dependencies
jest.mock('../../services/customer-services');
jest.mock('../../clients/cognito/cognito-client');
jest.mock('../../middleware/userAuth');
jest.mock('../../middleware/validationMid');
jest.mock('../../utils/logger');

const CustomerServices = require('../../services/customer-services');
const CognitoClient = require('../../clients/cognito/cognito-client');
const { authenticateRegistrationTokenV1, decodeBodyPayload } = require('../../middleware/userAuth');
const { validator } = require('../../middleware/validationMid');

describe('AuthController', () => {
  let app;
  let mockCustomerService;

  beforeEach(() => {
    // Setup Express app for testing
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Setup middleware mocks
    authenticateRegistrationTokenV1.mockImplementation(mockMiddleware.authenticateRegistrationToken);
    decodeBodyPayload.mockImplementation(mockMiddleware.decodeBodyPayload);
    validator.mockImplementation(() => mockMiddleware.validator());

    // Setup service mocks
    mockCustomerService = createMockService({
      login: jest.fn(),
      logout: jest.fn(),
      resetPassword: jest.fn(),
      forgotPassword: jest.fn(),
      createCognitoAccount: jest.fn(),
      deleteContact: jest.fn(),
      mapSubscription: jest.fn(),
      signupQueue: jest.fn()
    });
    CustomerServices.mockImplementation(() => mockCustomerService);

    // Setup Cognito client mock
    CognitoClient.mockImplementation(() => mockCognitoClient);

    // Setup routes
    app.post('/auth/login', AuthController.userLogin);
    app.post('/auth/register', AuthController.userSignupQueue);
    app.post('/auth/forgot-password', AuthController.userForgotPassword);
    app.post('/auth/reset-password', AuthController.userResetPassword);
    app.post('/auth/cognito/create-user', AuthController.userCreateCognitoAccount);
    app.post('/auth/delete-contact', AuthController.deleteContact);
    app.post('/auth/map-subscription', AuthController.mapSubscription);
  });

  describe('POST /auth/login', () => {
    it('should successfully login a user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const loginResult = {
        status: true,
        data: {
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          user: mockResponses.customer
        }
      };

      mockCustomerService.login.mockResolvedValue(loginResult);

      const response = await request(app)
        .post('/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: loginResult.data
      });
      expect(mockCustomerService.login).toHaveBeenCalledWith(
        loginData.email,
        loginData.password,
        expect.any(Object)
      );
    });

    it('should handle login failure', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const loginResult = {
        status: false,
        message: 'Invalid credentials'
      };

      mockCustomerService.login.mockResolvedValue(loginResult);

      await request(app)
        .post('/auth/login')
        .send(loginData)
        .expect(400);

      expect(mockCustomerService.login).toHaveBeenCalledWith(
        loginData.email,
        loginData.password,
        expect.any(Object)
      );
    });

    it('should handle service errors', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      mockCustomerService.login.mockRejectedValue(new Error('Service error'));

      await request(app)
        .post('/auth/login')
        .send(loginData)
        .expect(500);
    });
  });

  describe('POST /auth/register', () => {
    it('should successfully register a user', async () => {
      const registerData = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '555-1234',
        password: 'password123'
      };

      const registerResult = {
        status: true,
        data: { message: 'Registration successful' }
      };

      mockCustomerService.signupQueue.mockResolvedValue(registerResult);

      const response = await request(app)
        .post('/auth/register')
        .send(registerData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: registerResult.data
      });
      expect(mockCustomerService.signupQueue).toHaveBeenCalledWith(
        registerData,
        expect.any(Object)
      );
    });

    it('should handle registration failure', async () => {
      const registerData = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '555-1234',
        password: 'password123'
      };

      const registerResult = {
        status: false,
        message: 'User already exists'
      };

      mockCustomerService.signupQueue.mockResolvedValue(registerResult);

      await request(app)
        .post('/auth/register')
        .send(registerData)
        .expect(400);
    });
  });

  describe('POST /auth/forgot-password', () => {
    it('should successfully initiate forgot password', async () => {
      const forgotPasswordData = {
        email: '<EMAIL>'
      };

      const forgotPasswordResult = {
        status: true,
        data: { message: 'Password reset email sent' }
      };

      mockCustomerService.forgotPassword.mockResolvedValue(forgotPasswordResult);

      const response = await request(app)
        .post('/auth/forgot-password')
        .send(forgotPasswordData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: forgotPasswordResult.data
      });
      expect(mockCustomerService.forgotPassword).toHaveBeenCalledWith(
        forgotPasswordData.email,
        expect.any(Object)
      );
    });

    it('should handle forgot password failure', async () => {
      const forgotPasswordData = {
        email: '<EMAIL>'
      };

      const forgotPasswordResult = {
        status: false,
        message: 'User not found'
      };

      mockCustomerService.forgotPassword.mockResolvedValue(forgotPasswordResult);

      await request(app)
        .post('/auth/forgot-password')
        .send(forgotPasswordData)
        .expect(400);
    });
  });

  describe('POST /auth/reset-password', () => {
    it('should successfully reset password', async () => {
      const resetPasswordData = {
        email: '<EMAIL>',
        confirmationCode: '123456',
        newPassword: 'newpassword123'
      };

      const resetPasswordResult = {
        status: true,
        data: { message: 'Password reset successful' }
      };

      mockCustomerService.resetPassword.mockResolvedValue(resetPasswordResult);

      const response = await request(app)
        .post('/auth/reset-password')
        .send(resetPasswordData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: resetPasswordResult.data
      });
      expect(mockCustomerService.resetPassword).toHaveBeenCalledWith(
        resetPasswordData.email,
        resetPasswordData.confirmationCode,
        resetPasswordData.newPassword,
        expect.any(Object)
      );
    });

    it('should handle invalid confirmation code', async () => {
      const resetPasswordData = {
        email: '<EMAIL>',
        confirmationCode: 'invalid',
        newPassword: 'newpassword123'
      };

      const resetPasswordResult = {
        status: false,
        message: 'Invalid confirmation code'
      };

      mockCustomerService.resetPassword.mockResolvedValue(resetPasswordResult);

      await request(app)
        .post('/auth/reset-password')
        .send(resetPasswordData)
        .expect(400);
    });
  });

  describe('POST /auth/cognito/create-user', () => {
    it('should successfully create Cognito user', async () => {
      const createUserData = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        temporaryPassword: 'TempPass123!'
      };

      const createUserResult = {
        status: true,
        data: { userId: 'test-user-id' }
      };

      mockCustomerService.createCognitoAccount.mockResolvedValue(createUserResult);

      const response = await request(app)
        .post('/auth/cognito/create-user')
        .send(createUserData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: createUserResult.data
      });
      expect(mockCustomerService.createCognitoAccount).toHaveBeenCalledWith(
        createUserData,
        expect.any(Object)
      );
    });
  });

  describe('POST /auth/delete-contact', () => {
    it('should successfully delete contact', async () => {
      const deleteContactData = {
        email: '<EMAIL>'
      };

      const deleteContactResult = {
        status: true,
        data: { message: 'Contact deleted successfully' }
      };

      mockCustomerService.deleteContact.mockResolvedValue(deleteContactResult);

      const response = await request(app)
        .post('/auth/delete-contact')
        .send(deleteContactData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: deleteContactResult.data
      });
      expect(mockCustomerService.deleteContact).toHaveBeenCalledWith(
        deleteContactData.email,
        expect.any(Object)
      );
    });
  });

  describe('POST /auth/map-subscription', () => {
    it('should successfully map subscription', async () => {
      const mapSubscriptionData = {
        customerId: 'test-customer-id',
        subscriptionId: 'test-subscription-id'
      };

      const mapSubscriptionResult = {
        status: true,
        data: { message: 'Subscription mapped successfully' }
      };

      mockCustomerService.mapSubscription.mockResolvedValue(mapSubscriptionResult);

      const response = await request(app)
        .post('/auth/map-subscription')
        .send(mapSubscriptionData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: mapSubscriptionResult.data
      });
      expect(mockCustomerService.mapSubscription).toHaveBeenCalledWith(
        mapSubscriptionData,
        expect.any(Object)
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle service exceptions gracefully', async () => {
      mockCustomerService.login.mockRejectedValue(new Error('Database connection failed'));

      await request(app)
        .post('/auth/login')
        .send({ email: '<EMAIL>', password: 'password123' })
        .expect(500);
    });

    it('should handle validation errors', async () => {
      // This would be handled by validation middleware in real scenario
      await request(app)
        .post('/auth/login')
        .send({ email: 'invalid-email' }) // Missing password
        .expect(500);
    });
  });
});
