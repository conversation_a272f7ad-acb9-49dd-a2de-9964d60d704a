// Centralized mock exports for salesforce-sync tests

// Database mocks
const mockPool = {
  query: jest.fn(),
  end: jest.fn(),
  connect: jest.fn(),
  release: jest.fn()
};

// Salesforce client mock
const mockSalesforceClient = {
  login: jest.fn(),
  query: jest.fn(),
  sobject: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  fetchAllRecords: jest.fn(),
  getDeletedData: jest.fn(),
  getAddressDetailsById: jest.fn(),
  getDetailsByFieldAndApi: jest.fn()
};

// Elasticsearch client mock
const mockElasticsearchClient = {
  index: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  search: jest.fn(),
  indices: {
    create: jest.fn(),
    exists: jest.fn(),
    delete: jest.fn()
  }
};

// Enhanced logger mock
const mockEnhancedLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  logCronStart: jest.fn(),
  logCronEnd: jest.fn(),
  logApiRequest: jest.fn(),
  logApiResponse: jest.fn(),
  logDatabaseOperation: jest.fn(),
  logSalesforceOperation: jest.fn(),
  logSyncOperation: jest.fn(),
  createTimer: jest.fn(() => ({
    end: jest.fn()
  }))
};

// Chargebee client mock
const mockChargebeeClient = {
  invoice: {
    list: jest.fn()
  },
  subscription: {
    list: jest.fn(),
    retrieve: jest.fn()
  }
};

// Axios mock
const mockAxios = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  create: jest.fn(() => mockAxios)
};

// Node-cron mock
const mockNodeCron = {
  schedule: jest.fn()
};

// Express mock
const mockExpress = {
  Router: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    use: jest.fn()
  })),
  json: jest.fn(),
  urlencoded: jest.fn(),
  static: jest.fn()
};

// Mock factory functions
const createMockService = (methods = {}) => {
  const defaultMethods = {
    executeQuery: jest.fn(),
    getContactDetails: jest.fn(),
    checkAndManageContacts: jest.fn(),
    updateContacts: jest.fn(),
    deleteContact: jest.fn()
  };
  
  return {
    ...defaultMethods,
    ...methods
  };
};

const createMockController = (methods = {}) => {
  const defaultMethods = {
    deleteContact: jest.fn(),
    getContactsAndSync: jest.fn(),
    getOrderssAndSync: jest.fn()
  };
  
  return {
    ...defaultMethods,
    ...methods
  };
};

// Mock responses
const mockResponses = {
  salesforceContact: {
    Id: 'test-sf-id',
    FirstName: 'Test',
    LastName: 'User',
    Email: '<EMAIL>',
    Phone: '555-1234',
    LastModifiedDate: new Date().toISOString()
  },
  
  elasticResponse: {
    _id: 'test-elastic-id',
    _index: 'test-index',
    _version: 1,
    result: 'created'
  },
  
  dbQueryResult: {
    rows: [],
    rowCount: 0,
    command: 'SELECT'
  },
  
  syncResult: {
    status: true,
    contactCount: 5,
    successfulRecords: 5,
    totalRecords: 5,
    message: 'Sync completed successfully'
  }
};

module.exports = {
  mockPool,
  mockSalesforceClient,
  mockElasticsearchClient,
  mockEnhancedLogger,
  mockChargebeeClient,
  mockAxios,
  mockNodeCron,
  mockExpress,
  createMockService,
  createMockController,
  mockResponses
};
