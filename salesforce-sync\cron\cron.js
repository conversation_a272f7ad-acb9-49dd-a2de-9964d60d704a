const cron = require('node-cron');
const timezone = "America/Halifax";
const CONFIG = require('../config');
const EnhancedLogger = require('../utils/enhanced-logger');

const scheduled = CONFIG.NODE_ENV !== "local";
const logger = new EnhancedLogger();

// Services
const ContactTestService = require("../services/process-services/contact-sync-services");
const contactTestService = new ContactTestService();

module.exports = () => {

  // Cron for contact sync from Salesforce
  // Run at midnight every day
  cron.schedule("0 0 * * *", async function () {
    const cronName = "contact_sync_daily";
    const logMeta = { cronSchedule: "0 0 * * *" };

    // Log cron job start
    const { _id, _index } = await logger.logCronStart(cronName, logMeta);

    try {
      // Execute the sync operation
      const result = await contactTestService.getContactDetails();

      // Log cron job completion with results
      await logger.logCronEnd(cronName, true, {
        elasticId: _id,
        elasticIndex: _index,
        result: {
          contactsProcessed: result?.contactCount || 0,
          status: result?.status ? 'success' : 'failed',
          message: result?.message || 'Contact sync completed'
        }
      });
    } catch (error) {
      // Log error details
      await logger.logCronEnd(cronName, false, {
        elasticId: _id,
        elasticIndex: _index,
        error: {
          message: error.message,
          stack: error.stack
        }
      });

      // Also log to console for immediate visibility
      console.error("Error in syncing contact details:", error.message);
    }
  }, {
    scheduled,
    timezone
  });
}