# Enhanced Logging and Testing Guide

This guide provides comprehensive documentation for the enhanced logging system and test suite implemented across the Purple Cow Portal applications.

## Table of Contents

1. [Enhanced Logging System](#enhanced-logging-system)
2. [Test Suite Overview](#test-suite-overview)
3. [Setup Instructions](#setup-instructions)
4. [Usage Examples](#usage-examples)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)

## Enhanced Logging System

### Overview

The enhanced logging system integrates Winston with Elasticsearch to provide comprehensive logging across all applications:

- **Salesforce Sync**: Logs cron jobs, API operations, and sync processes
- **Webhook**: Logs incoming webhooks, XML parsing, and data processing
- **Node Application**: Logs API requests, authentication, and business operations

### Key Features

- **Structured Logging**: JSON-formatted logs with consistent metadata
- **Multiple Transports**: Console, file rotation, and Elasticsearch
- **Performance Monitoring**: Built-in timing and metrics collection
- **Error Tracking**: Comprehensive error logging with stack traces
- **Request Correlation**: Request IDs for tracing across services

### Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Application   │───▶│  Enhanced Logger │───▶│   Elasticsearch │
│                 │    │                  │    │                 │
│  - Controllers  │    │  - Winston       │    │  - Structured   │
│  - Services     │    │  - File Rotation │    │    Logs         │
│  - Middleware   │    │  - Performance   │    │  - Searchable   │
│  - Cron Jobs    │    │    Timers        │    │  - Dashboards   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Log Levels and Indices

#### Salesforce Sync
- `cron_logs`: Cron job execution logs
- `api_request_logs`: API request details
- `api_response_logs`: API response details
- `database_logs`: Database operations
- `salesforce_logs`: Salesforce API interactions
- `sync_logs`: Data synchronization processes
- `performance_logs`: Performance metrics

#### Webhook
- `webhook_received_logs`: Incoming webhook requests
- `webhook_processing_logs`: Webhook data processing
- `webhook_error_logs`: Webhook processing errors
- `xml_parsing_logs`: XML parsing results
- `authentication_logs`: Authentication attempts
- `data_transformation_logs`: Data transformation operations

#### Node Application
- `application_startup_logs`: Application startup events
- `api_operations_logs`: API operation logs
- `authentication_logs`: User authentication events
- `business_logic_logs`: Business operation logs

## Test Suite Overview

### Testing Framework

- **Jest**: Primary testing framework
- **Supertest**: HTTP assertion library for API testing
- **Comprehensive Mocking**: All external dependencies mocked

### Test Structure

```
tests/
├── setup.js                 # Global test configuration
├── mocks/
│   └── index.js             # Centralized mock utilities
├── controllers/             # Controller unit tests
├── services/                # Service layer tests
├── integration/             # End-to-end integration tests
└── utils/                   # Test utility functions
```

### Test Categories

1. **Unit Tests**: Individual component testing
2. **Integration Tests**: End-to-end workflow testing
3. **API Tests**: HTTP endpoint testing
4. **Service Tests**: Business logic testing
5. **Error Handling Tests**: Error scenario validation

## Setup Instructions

### Prerequisites

- Node.js 16+ 
- Elasticsearch 8.x
- Jest 29+

### Installation

1. **Install Dependencies**
   ```bash
   # Salesforce Sync
   cd salesforce-sync
   npm install

   # Webhook
   cd ../webhook
   npm install

   # Node Application
   cd ../node
   npm install
   ```

2. **Configure Environment Variables**
   ```bash
   # Add to .env files
   NODE_ENV=development
   ELASTICSEARCH_URL=http://localhost:9200
   LOG_LEVEL=info
   ```

3. **Setup Elasticsearch Indices**
   ```bash
   # Run index creation script
   node scripts/setup-elasticsearch-indices.js
   ```

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- controllers/auth-controller.test.js

# Run integration tests only
npm test -- integration/
```

## Usage Examples

### Enhanced Logger Usage

#### Basic Logging
```javascript
const EnhancedLogger = require('./utils/enhanced-logger');
const logger = new EnhancedLogger();

// Simple logging
await logger.info('Operation completed', { userId: '123' }, 'api_operations_logs');
await logger.error('Operation failed', { error: err.message }, 'api_error_logs');
```

#### Performance Monitoring
```javascript
const timer = logger.createTimer('database_operation');
// ... perform operation
await timer.end({ recordsProcessed: 100, success: true });
```

#### Cron Job Logging
```javascript
const cronName = 'daily_sync';
await logger.logCronStart(cronName, { scheduledTime: '00:00' });
// ... perform sync
await logger.logCronEnd(cronName, success, { recordsProcessed: 500 });
```

#### API Request/Response Logging
```javascript
// In middleware
await logger.logApiRequest(req, { customData: 'value' });
await logger.logApiResponse(req, res, responseTime, { result: 'success' });
```

### Test Examples

#### Controller Testing
```javascript
describe('AuthController', () => {
  it('should successfully login a user', async () => {
    mockCustomerService.login.mockResolvedValue({
      status: true,
      data: { accessToken: 'token', user: mockUser }
    });

    const response = await request(app)
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password' })
      .expect(200);

    expect(response.body.status).toBe(200);
    expect(mockCustomerService.login).toHaveBeenCalledWith(
      '<EMAIL>',
      'password',
      expect.any(Object)
    );
  });
});
```

#### Service Testing
```javascript
describe('CustomerServices', () => {
  it('should get customer details', async () => {
    mockModel.findOne.mockResolvedValue(mockCustomer);

    const result = await customerService.getCustomerDetails('user-id', mockElasticLogObj);

    expect(result.status).toBe(true);
    expect(result.data).toEqual(mockCustomer.dataValues);
  });
});
```

#### Integration Testing
```javascript
describe('Authentication Flow Integration', () => {
  it('should handle complete login flow', async () => {
    // Setup mocks for entire flow
    mockCognitoClient.adminInitiateAuth.mockResolvedValue(mockAuthResult);
    mockModel.findOne.mockResolvedValue(mockCustomer);

    const response = await request(app)
      .post('/auth/login')
      .send(loginData)
      .expect(200);

    expect(response.body.data.accessToken).toBeDefined();
  });
});
```

## Best Practices

### Logging Best Practices

1. **Use Appropriate Log Levels**
   - `error`: System errors, exceptions
   - `warn`: Warnings, deprecated usage
   - `info`: General information, successful operations
   - `debug`: Detailed debugging information

2. **Include Context**
   ```javascript
   await logger.info('User login', {
     userId: user.id,
     email: user.email,
     timestamp: new Date().toISOString(),
     requestId: req.logData.requestId
   });
   ```

3. **Use Performance Timers**
   ```javascript
   const timer = logger.createTimer('operation_name');
   // ... operation
   await timer.end({ success: true, recordCount: 100 });
   ```

4. **Log Errors with Stack Traces**
   ```javascript
   await logger.error('Operation failed', {
     error: error.message,
     stack: error.stack,
     context: { userId, operation }
   });
   ```

### Testing Best Practices

1. **Use Descriptive Test Names**
   ```javascript
   it('should return 400 when email is missing from login request', async () => {
     // Test implementation
   });
   ```

2. **Setup and Teardown**
   ```javascript
   beforeEach(() => {
     jest.clearAllMocks();
   });

   afterEach(() => {
     jest.restoreAllMocks();
   });
   ```

3. **Mock External Dependencies**
   ```javascript
   jest.mock('../../clients/cognito-client');
   jest.mock('../../services/customer-services');
   ```

4. **Test Error Scenarios**
   ```javascript
   it('should handle service errors gracefully', async () => {
     mockService.method.mockRejectedValue(new Error('Service error'));
     
     await request(app)
       .post('/endpoint')
       .send(data)
       .expect(500);
   });
   ```

## Troubleshooting

### Common Issues

1. **Elasticsearch Connection Issues**
   ```bash
   # Check Elasticsearch status
   curl -X GET "localhost:9200/_cluster/health"
   
   # Verify indices exist
   curl -X GET "localhost:9200/_cat/indices"
   ```

2. **Test Failures**
   ```bash
   # Run tests with verbose output
   npm test -- --verbose
   
   # Run specific failing test
   npm test -- --testNamePattern="specific test name"
   ```

3. **Mock Issues**
   ```javascript
   // Clear mocks between tests
   beforeEach(() => {
     jest.clearAllMocks();
   });
   
   // Reset modules if needed
   beforeEach(() => {
     jest.resetModules();
   });
   ```

### Performance Optimization

1. **Elasticsearch Index Management**
   - Use appropriate index patterns
   - Configure index lifecycle policies
   - Monitor index size and performance

2. **Test Performance**
   - Use `jest.setTimeout()` for long-running tests
   - Mock heavy operations
   - Run tests in parallel when possible

### Monitoring and Alerts

1. **Log Monitoring**
   - Set up Elasticsearch alerts for error rates
   - Monitor log volume and patterns
   - Create dashboards for key metrics

2. **Test Monitoring**
   - Track test coverage metrics
   - Monitor test execution time
   - Set up CI/CD pipeline alerts

## Conclusion

This enhanced logging and testing system provides comprehensive observability and quality assurance for the Purple Cow Portal applications. The structured approach ensures maintainability, debuggability, and reliability across all services.

For additional support or questions, refer to the individual application documentation or contact the development team.
