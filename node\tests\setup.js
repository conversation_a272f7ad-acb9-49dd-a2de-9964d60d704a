// Global test setup for node application
const path = require('path');

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_PREFIX = 'test';

// Mock console methods to reduce noise during tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  // Helper to create mock request objects
  createMockRequest: (overrides = {}) => ({
    method: 'GET',
    url: '/test',
    query: {},
    body: {},
    params: {},
    headers: {},
    get: jest.fn((header) => overrides.headers?.[header.toLowerCase()]),
    ip: '127.0.0.1',
    userDetails: {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User'
    },
    elasticLogObj: {
      requestId: 'test-request-id',
      timestamp: new Date().toISOString()
    },
    ...overrides
  }),

  // Helper to create mock response objects
  createMockResponse: (overrides = {}) => {
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      get: jest.fn(),
      locals: {},
      statusCode: 200,
      on: jest.fn(),
      sendFile: jest.fn(),
      redirect: jest.fn(),
      ...overrides
    };
    return res;
  },

  // Helper to create mock next function
  createMockNext: () => jest.fn(),

  // Helper to wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to create mock database results
  createMockDbResult: (data = [], overrides = {}) => ({
    rows: data,
    rowCount: data.length,
    command: 'SELECT',
    ...overrides
  }),

  // Helper to create mock Sequelize model results
  createMockSequelizeResult: (data = {}, overrides = {}) => ({
    dataValues: data,
    get: jest.fn((key) => data[key]),
    set: jest.fn(),
    save: jest.fn(),
    destroy: jest.fn(),
    update: jest.fn(),
    ...overrides
  }),

  // Helper to create mock customer data
  createMockCustomer: (overrides = {}) => ({
    id: 'test-customer-id',
    firstName: 'Test',
    lastName: 'Customer',
    email: '<EMAIL>',
    phone: '555-1234',
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  }),

  // Helper to create mock card data
  createMockCard: (overrides = {}) => ({
    id: 'test-card-id',
    customerId: 'test-customer-id',
    last4: '1234',
    brand: 'visa',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true,
    ...overrides
  }),

  // Helper to create mock subscription data
  createMockSubscription: (overrides = {}) => ({
    id: 'test-subscription-id',
    customerId: 'test-customer-id',
    planId: 'test-plan-id',
    status: 'active',
    currentTermStart: new Date().toISOString(),
    currentTermEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    ...overrides
  }),

  // Helper to create mock Chargebee response
  createMockChargebeeResponse: (data = {}, overrides = {}) => ({
    customer: data.customer || testUtils.createMockCustomer(),
    subscription: data.subscription || testUtils.createMockSubscription(),
    card: data.card || testUtils.createMockCard(),
    ...overrides
  }),

  // Helper to create mock Cognito response
  createMockCognitoResponse: (overrides = {}) => ({
    User: {
      Username: 'test-user-id',
      Attributes: [
        { Name: 'email', Value: '<EMAIL>' },
        { Name: 'given_name', Value: 'Test' },
        { Name: 'family_name', Value: 'User' }
      ],
      UserStatus: 'CONFIRMED',
      Enabled: true
    },
    ...overrides
  }),

  // Helper to create mock JWT token
  createMockJwtToken: (payload = {}) => {
    const defaultPayload = {
      sub: 'test-user-id',
      email: '<EMAIL>',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    };
    return Buffer.from(JSON.stringify({ ...defaultPayload, ...payload })).toString('base64');
  },

  // Helper to create mock Elasticsearch response
  createMockElasticResponse: (overrides = {}) => ({
    _id: 'test-elastic-id',
    _index: 'test-index',
    _version: 1,
    result: 'created',
    ...overrides
  })
};

// Global beforeEach to reset all mocks
beforeEach(() => {
  jest.clearAllMocks();
});

// Global afterEach cleanup
afterEach(() => {
  jest.restoreAllMocks();
});

// Handle unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);
