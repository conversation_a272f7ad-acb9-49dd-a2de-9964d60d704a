{"name": "webhook", "version": "1.0.0", "description": "To receive webhook from salesforce", "main": "app.js", "scripts": {"start": "node app.js", "server": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "repository": {"type": "git", "url": "portal.purplecowinternet.com"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.687.0", "@elastic/elasticsearch": "^8.15.2", "axios": "^1.7.7", "body-parser-xml": "^2.0.5", "chargebee": "^3.6.1", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "jsforce": "^3.6.2", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mysql": "^2.18.1", "nodemon": "^3.1.7", "xml2js": "^0.6.2", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "winston-elasticsearch": "^0.17.4"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/tests/**", "!coverage/**"], "testMatch": ["**/tests/**/*.test.js"]}}