const request = require('supertest');
const express = require('express');
const SalesforceSyncController = require('../../controllers/salesforce-sync-controller');
const { mockEnhancedLogger, mockResponses, createMockService } = require('../mocks');

// Mock dependencies
jest.mock('../../utils/enhanced-logger');
jest.mock('../../services/process-services/contact-sync-services');
jest.mock('../../services/delete-contact-service');
jest.mock('../../services/process-services/order-services');

const ContactTestService = require('../../services/process-services/contact-sync-services');
const DeleteContactService = require('../../services/delete-contact-service');
const OrderTestServices = require('../../services/process-services/order-services');

describe('SalesforceSyncController', () => {
  let app;
  let mockContactService;
  let mockDeleteService;
  let mockOrderService;

  beforeEach(() => {
    // Setup Express app for testing
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Add mock request logging data
    app.use((req, res, next) => {
      req.logData = { requestId: 'test-request-id', startTime: Date.now() };
      next();
    });

    // Setup routes
    app.post('/delete-contact', SalesforceSyncController.deleteContact.bind(SalesforceSyncController));
    app.get('/get-contacts-and-sync', SalesforceSyncController.getContactsAndSync.bind(SalesforceSyncController));
    app.get('/get-orders-and-sync', SalesforceSyncController.getOrderssAndSync.bind(SalesforceSyncController));

    // Setup service mocks
    mockContactService = createMockService({
      getContactDetails: jest.fn()
    });
    mockDeleteService = createMockService({
      deleteContact: jest.fn()
    });
    mockOrderService = createMockService({
      getCreationOrderDetails: jest.fn()
    });

    ContactTestService.mockImplementation(() => mockContactService);
    DeleteContactService.mockImplementation(() => mockDeleteService);
    OrderTestServices.mockImplementation(() => mockOrderService);
  });

  describe('POST /delete-contact', () => {
    it('should successfully delete a contact', async () => {
      const email = '<EMAIL>';
      mockDeleteService.deleteContact.mockResolvedValue({ success: true });

      const response = await request(app)
        .post('/delete-contact')
        .send({ email })
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        email
      });
      expect(mockDeleteService.deleteContact).toHaveBeenCalledWith(email);
    });

    it('should return error when email is not provided', async () => {
      const response = await request(app)
        .post('/delete-contact')
        .send({})
        .expect(500);

      expect(mockDeleteService.deleteContact).not.toHaveBeenCalled();
    });

    it('should handle service errors gracefully', async () => {
      const email = '<EMAIL>';
      const error = new Error('Service error');
      mockDeleteService.deleteContact.mockRejectedValue(error);

      await request(app)
        .post('/delete-contact')
        .send({ email })
        .expect(500);

      expect(mockDeleteService.deleteContact).toHaveBeenCalledWith(email);
    });

    it('should log the operation correctly', async () => {
      const email = '<EMAIL>';
      mockDeleteService.deleteContact.mockResolvedValue({ success: true });

      await request(app)
        .post('/delete-contact')
        .send({ email })
        .expect(200);

      // Verify logging calls were made
      expect(mockEnhancedLogger.prototype.info).toHaveBeenCalledWith(
        'Delete contact request received',
        expect.objectContaining({ email }),
        'api_operations_logs'
      );
    });
  });

  describe('GET /get-contacts-and-sync', () => {
    it('should successfully sync all contacts', async () => {
      const syncResult = {
        ...mockResponses.syncResult,
        contactCount: 10
      };
      mockContactService.getContactDetails.mockResolvedValue(syncResult);

      const response = await request(app)
        .get('/get-contacts-and-sync')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        contactsProcessed: 10,
        status: true
      });
      expect(mockContactService.getContactDetails).toHaveBeenCalledWith(undefined);
    });

    it('should successfully sync single contact by email', async () => {
      const email = '<EMAIL>';
      const syncResult = {
        ...mockResponses.syncResult,
        contactCount: 1
      };
      mockContactService.getContactDetails.mockResolvedValue(syncResult);

      const response = await request(app)
        .get('/get-contacts-and-sync')
        .query({ email })
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        contactsProcessed: 1,
        status: true
      });
      expect(mockContactService.getContactDetails).toHaveBeenCalledWith(email);
    });

    it('should handle sync failures', async () => {
      const syncResult = {
        status: false,
        contactCount: 0,
        error: 'Sync failed'
      };
      mockContactService.getContactDetails.mockResolvedValue(syncResult);

      const response = await request(app)
        .get('/get-contacts-and-sync')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        contactsProcessed: 0,
        status: false
      });
    });

    it('should handle service errors', async () => {
      const error = new Error('Service error');
      mockContactService.getContactDetails.mockRejectedValue(error);

      await request(app)
        .get('/get-contacts-and-sync')
        .expect(500);
    });

    it('should log sync operations correctly', async () => {
      const email = '<EMAIL>';
      mockContactService.getContactDetails.mockResolvedValue(mockResponses.syncResult);

      await request(app)
        .get('/get-contacts-and-sync')
        .query({ email })
        .expect(200);

      expect(mockEnhancedLogger.prototype.info).toHaveBeenCalledWith(
        'Contacts sync request received',
        expect.objectContaining({
          email,
          syncType: 'single_contact'
        }),
        'api_operations_logs'
      );
    });
  });

  describe('GET /get-orders-and-sync', () => {
    it('should successfully sync orders', async () => {
      const orderResult = {
        status: true,
        orderCount: 5
      };
      mockOrderService.getCreationOrderDetails.mockResolvedValue(orderResult);

      const response = await request(app)
        .get('/get-orders-and-sync')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        ordersProcessed: 5,
        status: true
      });
      expect(mockOrderService.getCreationOrderDetails).toHaveBeenCalled();
    });

    it('should handle order sync failures', async () => {
      const orderResult = {
        status: false,
        orderCount: 0,
        error: 'Order sync failed'
      };
      mockOrderService.getCreationOrderDetails.mockResolvedValue(orderResult);

      const response = await request(app)
        .get('/get-orders-and-sync')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        ordersProcessed: 0,
        status: false
      });
    });

    it('should handle service errors', async () => {
      const error = new Error('Order service error');
      mockOrderService.getCreationOrderDetails.mockRejectedValue(error);

      await request(app)
        .get('/get-orders-and-sync')
        .expect(500);
    });

    it('should log order sync operations', async () => {
      mockOrderService.getCreationOrderDetails.mockResolvedValue({ status: true, orderCount: 3 });

      await request(app)
        .get('/get-orders-and-sync')
        .expect(200);

      expect(mockEnhancedLogger.prototype.info).toHaveBeenCalledWith(
        'Orders sync request received',
        expect.objectContaining({ requestId: 'test-request-id' }),
        'api_operations_logs'
      );
    });
  });

  describe('Performance and Timing', () => {
    it('should create and end timers for all operations', async () => {
      const mockTimer = { end: jest.fn() };
      mockEnhancedLogger.prototype.createTimer.mockReturnValue(mockTimer);
      
      mockContactService.getContactDetails.mockResolvedValue(mockResponses.syncResult);

      await request(app)
        .get('/get-contacts-and-sync')
        .expect(200);

      expect(mockEnhancedLogger.prototype.createTimer).toHaveBeenCalledWith('contacts_sync_operation');
      expect(mockTimer.end).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        contactsProcessed: expect.any(Number)
      }));
    });
  });
});
