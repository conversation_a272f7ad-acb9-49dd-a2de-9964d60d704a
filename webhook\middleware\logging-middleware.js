const EnhancedLogger = require('../utils/enhanced-logger');

class LoggingMiddleware {
    constructor() {
        this.logger = new EnhancedLogger();
    }

    // Request logging middleware
    requestLogger() {
        return async (req, res, next) => {
            const startTime = Date.now();
            
            // For webhook requests, we'll log more details after XML parsing
            // Just capture basic info here
            req.logData = {
                startTime,
                requestId: this.generateRequestId()
            };

            next();
        };
    }

    // Response logging middleware
    responseLogger() {
        return async (req, res, next) => {
            const originalSend = res.send;

            // Override res.send
            res.send = function(data) {
                res.locals.responseData = data;
                return originalSend.call(this, data);
            };

            // Log response when request finishes
            res.on('finish', async () => {
                if (req.logData) {
                    const responseTime = Date.now() - req.logData.startTime;
                    
                    // For webhook responses, we log minimal data as the response is usually XML
                    await this.logger.info(`Webhook response sent: ${res.statusCode}`, {
                        requestId: req.logData.requestId,
                        responseTime: `${responseTime}ms`,
                        statusCode: res.statusCode,
                        contentType: res.get('Content-Type'),
                        url: req.url,
                        method: req.method
                    }, 'webhook_response_logs');
                }
            });

            next();
        };
    }

    // Webhook specific logging middleware
    webhookLogger() {
        return async (req, res, next) => {
            // This middleware is called after XML parsing when we have webhook data
            if (req.webhookData && req.sfApiName && req.organizationId) {
                const elasticResult = await this.logger.logWebhookReceived(
                    req, 
                    req.organizationId, 
                    req.sfApiName,
                    {
                        requestId: req.logData?.requestId,
                        webhookData: {
                            id: req.webhookData.Id,
                            type: req.sfApiName,
                            lastModifiedDate: req.webhookData.LastModifiedDate
                        }
                    }
                );

                // Update request log data with elastic info
                if (req.logData) {
                    req.logData.elasticId = elasticResult?._id;
                    req.logData.elasticIndex = elasticResult?._index;
                }
            }

            next();
        };
    }

    // Error logging middleware
    errorLogger() {
        return async (err, req, res, next) => {
            const errorData = {
                error: err.message,
                stack: err.stack,
                requestId: req.logData?.requestId,
                method: req.method,
                url: req.url,
                sfApiName: req.sfApiName,
                organizationId: req.organizationId,
                statusCode: err.statusCode || 500
            };

            await this.logger.error('Webhook Error occurred', errorData, 'webhook_error_logs');

            next(err);
        };
    }

    // Utility methods
    generateRequestId() {
        return `webhook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

module.exports = LoggingMiddleware;
