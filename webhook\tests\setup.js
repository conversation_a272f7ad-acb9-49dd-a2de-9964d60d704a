// Global test setup for webhook
const path = require('path');

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_PREFIX = 'test';

// Mock console methods to reduce noise during tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  // Helper to create mock request objects
  createMockRequest: (overrides = {}) => ({
    method: 'POST',
    url: '/webhook/upsert',
    query: {},
    body: {},
    params: {},
    headers: {
      'content-type': 'application/xml',
      'content-length': '1000'
    },
    get: jest.fn((header) => overrides.headers?.[header.toLowerCase()] || 
         ({ 'content-type': 'application/xml', 'content-length': '1000' })[header.toLowerCase()]),
    ip: '127.0.0.1',
    logData: {
      requestId: 'webhook-test-request-id',
      startTime: Date.now()
    },
    ...overrides
  }),

  // Helper to create mock response objects
  createMockResponse: (overrides = {}) => {
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      get: jest.fn(),
      locals: {},
      statusCode: 200,
      on: jest.fn(),
      ...overrides
    };
    return res;
  },

  // Helper to create mock next function
  createMockNext: () => jest.fn(),

  // Helper to wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to create mock webhook XML data
  createMockWebhookXml: (sfApiName = 'Contact', overrides = {}) => {
    const baseXml = `<?xml version="1.0" encoding="UTF-8"?>
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
      <soapenv:Body>
        <notifications xmlns="http://soap.sforce.com/2005/09/outbound">
          <OrganizationId>00DSv000000nGA5MAM</OrganizationId>
          <ActionId>04kSv0000004SlhIAE</ActionId>
          <SessionId>test-session-id</SessionId>
          <Notification>
            <Id>test-notification-id</Id>
            <sObject xsi:type="sf:${sfApiName}" xmlns:sf="urn:sobject.enterprise.soap.sforce.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <sf:Id>test-record-id</sf:Id>
              <sf:Name>Test Record</sf:Name>
              <sf:LastModifiedDate>${new Date().toISOString()}</sf:LastModifiedDate>
            </sObject>
          </Notification>
        </notifications>
      </soapenv:Body>
    </soapenv:Envelope>`;
    return baseXml;
  },

  // Helper to create mock webhook data (parsed)
  createMockWebhookData: (overrides = {}) => ({
    Id: 'test-record-id',
    Name: 'Test Record',
    LastModifiedDate: new Date().toISOString(),
    CreatedDate: new Date().toISOString(),
    ...overrides
  }),

  // Helper to create mock database results
  createMockDbResult: (data = [], overrides = {}) => ({
    rows: data,
    rowCount: data.length,
    command: 'SELECT',
    ...overrides
  }),

  // Helper to create mock Elasticsearch response
  createMockElasticResponse: (overrides = {}) => ({
    _id: 'test-elastic-id',
    _index: 'test-index',
    _version: 1,
    result: 'created',
    ...overrides
  }),

  // Helper to create mock decrypted XML response
  createMockDecryptedXml: (overrides = {}) => ({
    status: true,
    webhookData: testUtils.createMockWebhookData(),
    sfApiName: 'Contact',
    organizationId: '00DSv000000nGA5MAM',
    sessionId: 'test-session-id',
    ...overrides
  })
};

// Global beforeEach to reset all mocks
beforeEach(() => {
  jest.clearAllMocks();
});

// Global afterEach cleanup
afterEach(() => {
  jest.restoreAllMocks();
});

// Handle unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);
