const request = require('supertest');
const express = require('express');
const CardController = require('../../controllers/card-controller');
const { mockResponses, createMockService, mockMiddleware } = require('../mocks');

// Mock dependencies
jest.mock('../../services/card-services');
jest.mock('../../middleware/userAuth');
jest.mock('../../middleware/validationMid');
jest.mock('../../utils/logger');

const CardServices = require('../../services/card-services');
const { authenticateToken } = require('../../middleware/userAuth');
const { validator } = require('../../middleware/validationMid');

describe('CardController', () => {
  let app;
  let mockCardService;

  beforeEach(() => {
    // Setup Express app for testing
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Setup middleware mocks
    authenticateToken.mockImplementation(mockMiddleware.authenticateToken);
    validator.mockImplementation(() => mockMiddleware.validator());

    // Setup service mocks
    mockCardService = createMockService({
      addCard: jest.fn(),
      updateCard: jest.fn(),
      deleteCard: jest.fn(),
      fetchCard: jest.fn()
    });
    CardServices.mockImplementation(() => mockCardService);

    // Setup routes
    app.post('/api/v1/card', CardController.addCard);
    app.put('/api/v1/card', CardController.updateCard);
    app.delete('/api/v1/card/:cardId', CardController.deleteCard);
    app.get('/api/v1/card', CardController.fetchCard);
  });

  describe('POST /api/v1/card', () => {
    it('should successfully add a new card', async () => {
      const cardData = {
        number: '****************',
        expiryMonth: 12,
        expiryYear: 2025,
        cvv: '123',
        holderName: 'Test User'
      };

      const addCardResult = {
        status: true,
        data: mockResponses.card
      };

      mockCardService.addCard.mockResolvedValue(addCardResult);

      const response = await request(app)
        .post('/api/v1/card')
        .send(cardData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: addCardResult.data
      });
      expect(mockCardService.addCard).toHaveBeenCalledWith(
        'test-user-id',
        cardData,
        expect.any(Object)
      );
    });

    it('should handle invalid card data', async () => {
      const cardData = {
        number: '1234567890123456', // Invalid card number
        expiryMonth: 12,
        expiryYear: 2025,
        cvv: '123',
        holderName: 'Test User'
      };

      const addCardResult = {
        status: false,
        message: 'Invalid card number'
      };

      mockCardService.addCard.mockResolvedValue(addCardResult);

      await request(app)
        .post('/api/v1/card')
        .send(cardData)
        .expect(400);
    });

    it('should handle service errors', async () => {
      const cardData = {
        number: '****************',
        expiryMonth: 12,
        expiryYear: 2025,
        cvv: '123',
        holderName: 'Test User'
      };

      mockCardService.addCard.mockRejectedValue(new Error('Payment gateway error'));

      await request(app)
        .post('/api/v1/card')
        .send(cardData)
        .expect(500);
    });
  });

  describe('PUT /api/v1/card', () => {
    it('should successfully update a card', async () => {
      const updateData = {
        cardId: 'test-card-id',
        expiryMonth: 6,
        expiryYear: 2026,
        holderName: 'Updated User'
      };

      const updateCardResult = {
        status: true,
        data: {
          ...mockResponses.card,
          expiryMonth: 6,
          expiryYear: 2026,
          holderName: 'Updated User'
        }
      };

      mockCardService.updateCard.mockResolvedValue(updateCardResult);

      const response = await request(app)
        .put('/api/v1/card')
        .send(updateData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: updateCardResult.data
      });
      expect(mockCardService.updateCard).toHaveBeenCalledWith(
        'test-user-id',
        updateData,
        expect.any(Object)
      );
    });

    it('should handle card not found', async () => {
      const updateData = {
        cardId: 'non-existent-card-id',
        expiryMonth: 6,
        expiryYear: 2026
      };

      const updateCardResult = {
        status: false,
        message: 'Card not found'
      };

      mockCardService.updateCard.mockResolvedValue(updateCardResult);

      await request(app)
        .put('/api/v1/card')
        .send(updateData)
        .expect(400);
    });
  });

  describe('DELETE /api/v1/card/:cardId', () => {
    it('should successfully delete a card', async () => {
      const cardId = 'test-card-id';

      const deleteCardResult = {
        status: true,
        data: { message: 'Card deleted successfully' }
      };

      mockCardService.deleteCard.mockResolvedValue(deleteCardResult);

      const response = await request(app)
        .delete(`/api/v1/card/${cardId}`)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: deleteCardResult.data
      });
      expect(mockCardService.deleteCard).toHaveBeenCalledWith(
        'test-user-id',
        cardId,
        expect.any(Object)
      );
    });

    it('should handle card not found for deletion', async () => {
      const cardId = 'non-existent-card-id';

      const deleteCardResult = {
        status: false,
        message: 'Card not found'
      };

      mockCardService.deleteCard.mockResolvedValue(deleteCardResult);

      await request(app)
        .delete(`/api/v1/card/${cardId}`)
        .expect(400);
    });

    it('should handle deletion of default card', async () => {
      const cardId = 'default-card-id';

      const deleteCardResult = {
        status: false,
        message: 'Cannot delete default card'
      };

      mockCardService.deleteCard.mockResolvedValue(deleteCardResult);

      await request(app)
        .delete(`/api/v1/card/${cardId}`)
        .expect(400);
    });
  });

  describe('GET /api/v1/card', () => {
    it('should successfully fetch all cards', async () => {
      const fetchCardResult = {
        status: true,
        data: [
          mockResponses.card,
          {
            ...mockResponses.card,
            id: 'test-card-id-2',
            last4: '5678',
            brand: 'mastercard',
            isDefault: false
          }
        ]
      };

      mockCardService.fetchCard.mockResolvedValue(fetchCardResult);

      const response = await request(app)
        .get('/api/v1/card')
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: fetchCardResult.data
      });
      expect(mockCardService.fetchCard).toHaveBeenCalledWith(
        'test-user-id',
        expect.any(Object)
      );
    });

    it('should handle no cards found', async () => {
      const fetchCardResult = {
        status: true,
        data: []
      };

      mockCardService.fetchCard.mockResolvedValue(fetchCardResult);

      const response = await request(app)
        .get('/api/v1/card')
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: []
      });
    });

    it('should handle service errors', async () => {
      mockCardService.fetchCard.mockRejectedValue(new Error('Database error'));

      await request(app)
        .get('/api/v1/card')
        .expect(500);
    });
  });

  describe('Error Handling', () => {
    it('should handle service exceptions gracefully', async () => {
      mockCardService.addCard.mockRejectedValue(new Error('Payment gateway timeout'));

      await request(app)
        .post('/api/v1/card')
        .send({
          number: '****************',
          expiryMonth: 12,
          expiryYear: 2025,
          cvv: '123',
          holderName: 'Test User'
        })
        .expect(500);
    });

    it('should handle validation errors', async () => {
      // This would be handled by validation middleware in real scenario
      await request(app)
        .post('/api/v1/card')
        .send({
          number: '****************'
          // Missing required fields
        })
        .expect(500);
    });
  });
});
