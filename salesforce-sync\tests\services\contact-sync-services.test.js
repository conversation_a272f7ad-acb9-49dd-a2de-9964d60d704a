const ContactSyncServices = require('../../services/process-services/contact-sync-services');
const { mockPool, mockSalesforceClient, mockElasticsearchClient, mockEnhancedLogger, mockResponses } = require('../mocks');

// Mock dependencies
jest.mock('../../db');
jest.mock('../../clients/salesforce-client');
jest.mock('../../clients/elastic-search');
jest.mock('../../utils/enhanced-logger');
jest.mock('../../helper/custom-helper');

const pool = require('../../db');
const SalesforceClient = require('../../clients/salesforce-client');
const ElasticSearch = require('../../clients/elastic-search');
const EnhancedLogger = require('../../utils/enhanced-logger');
const { getCurrentAtlanticTime } = require('../../helper/custom-helper');

describe('ContactSyncServices', () => {
  let contactSyncService;

  beforeEach(() => {
    // Setup mocks
    pool.query = mockPool.query;
    SalesforceClient.mockImplementation(() => mockSalesforceClient);
    ElasticSearch.mockImplementation(() => mockElasticsearchClient);
    EnhancedLogger.mockImplementation(() => mockEnhancedLogger);
    
    getCurrentAtlanticTime.mockReturnValue(new Date().toISOString());

    contactSyncService = new ContactSyncServices();
  });

  describe('executeQuery', () => {
    it('should successfully execute database query', async () => {
      const mockResult = [
        { id: 1, sf_record_id: 'sf123', email: '<EMAIL>' },
        { id: 2, sf_record_id: 'sf456', email: '<EMAIL>' }
      ];
      
      mockPool.query.mockResolvedValue(mockResult);

      const result = await contactSyncService.executeQuery('SELECT * FROM contacts');

      expect(result).toEqual(mockResult);
      expect(mockPool.query).toHaveBeenCalledWith('SELECT * FROM contacts');
    });

    it('should handle database query errors', async () => {
      const error = new Error('Database connection failed');
      mockPool.query.mockRejectedValue(error);

      await expect(contactSyncService.executeQuery('SELECT * FROM contacts'))
        .rejects.toThrow('Database connection failed');
    });
  });

  describe('getContactDetails', () => {
    beforeEach(() => {
      // Mock Elasticsearch operations
      mockElasticsearchClient.insertDocument.mockResolvedValue({
        _id: 'test-elastic-id',
        _index: 'sync_logs'
      });
      mockElasticsearchClient.updateDocument.mockResolvedValue({});
    });

    it('should successfully sync all contacts', async () => {
      const mockContacts = [
        { id: 1, sf_record_id: 'sf123' },
        { id: 2, sf_record_id: 'sf456' }
      ];

      // Mock database query
      contactSyncService.executeQuery = jest.fn().mockResolvedValue(mockContacts);
      
      // Mock other service methods
      contactSyncService.checkAndManageContacts = jest.fn().mockResolvedValue([
        { status: true, recordId: 'sf123' },
        { status: true, recordId: 'sf456' }
      ]);
      contactSyncService.getAndMapCardDetails = jest.fn().mockResolvedValue([]);
      contactSyncService.deleteDetails = jest.fn().mockResolvedValue({ execute: false });

      const result = await contactSyncService.getContactDetails();

      expect(result).toEqual({
        status: true,
        contactCount: 2,
        successfulRecords: 2,
        totalRecords: 2,
        message: '24-hour sync ended',
        syncRecords: [
          { status: true, recordId: 'sf123' },
          { status: true, recordId: 'sf456' }
        ]
      });

      expect(contactSyncService.executeQuery).toHaveBeenCalledWith(
        'SELECT id, sf_record_id FROM contacts'
      );
      expect(mockEnhancedLogger.logSyncOperation).toHaveBeenCalledWith(
        'cron',
        'started',
        expect.objectContaining({ contactCount: 2 })
      );
    });

    it('should successfully sync single contact by email', async () => {
      const email = '<EMAIL>';
      const mockContacts = [{ id: 1, sf_record_id: 'sf123' }];

      contactSyncService.executeQuery = jest.fn().mockResolvedValue(mockContacts);
      contactSyncService.checkAndManageSingleContacts = jest.fn().mockResolvedValue([
        { status: true, recordId: 'sf123' }
      ]);
      contactSyncService.getAndMapCardDetails = jest.fn().mockResolvedValue([]);

      const result = await contactSyncService.getContactDetails(email);

      expect(result).toEqual({
        status: true,
        contactCount: 1,
        successfulRecords: 1,
        totalRecords: 1,
        message: `Email: ${email} sync ended`,
        syncRecords: [{ status: true, recordId: 'sf123' }]
      });

      expect(contactSyncService.executeQuery).toHaveBeenCalledWith(
        `SELECT id, sf_record_id FROM contacts where email = '${email}'`
      );
      expect(mockEnhancedLogger.logSyncOperation).toHaveBeenCalledWith(
        'email',
        'started',
        expect.objectContaining({ contactCount: 1, email })
      );
    });

    it('should handle sync with errors', async () => {
      const mockContacts = [
        { id: 1, sf_record_id: 'sf123' },
        { id: 2, sf_record_id: 'sf456' }
      ];

      contactSyncService.executeQuery = jest.fn().mockResolvedValue(mockContacts);
      contactSyncService.checkAndManageContacts = jest.fn().mockResolvedValue([
        { status: true, recordId: 'sf123' },
        { status: false, recordId: 'sf456', error: 'Sync failed' }
      ]);
      contactSyncService.getAndMapCardDetails = jest.fn().mockResolvedValue([]);
      contactSyncService.deleteDetails = jest.fn().mockResolvedValue({ execute: false });

      const result = await contactSyncService.getContactDetails();

      expect(result).toEqual({
        status: false,
        contactCount: 2,
        successfulRecords: 1,
        totalRecords: 2,
        message: '24-hour sync ended',
        syncRecords: [
          { status: true, recordId: 'sf123' },
          { status: false, recordId: 'sf456', error: 'Sync failed' }
        ]
      });

      expect(mockEnhancedLogger.logSyncOperation).toHaveBeenCalledWith(
        'cron',
        'completed_with_errors',
        expect.objectContaining({ 
          contactCount: 2,
          successfulRecords: 1,
          totalRecords: 2
        })
      );
    });

    it('should handle sync service errors', async () => {
      const error = new Error('Salesforce API error');
      contactSyncService.executeQuery = jest.fn().mockRejectedValue(error);

      const result = await contactSyncService.getContactDetails();

      expect(result).toEqual({
        status: false,
        contactCount: 0,
        error: 'Salesforce API error',
        message: '24-hour sync failed',
        syncRecords: []
      });

      expect(mockEnhancedLogger.logSyncOperation).toHaveBeenCalledWith(
        'cron',
        'failed',
        expect.objectContaining({
          contactCount: 0,
          error: 'Salesforce API error'
        })
      );
    });

    it('should handle empty contact list', async () => {
      contactSyncService.executeQuery = jest.fn().mockResolvedValue([]);

      const result = await contactSyncService.getContactDetails();

      expect(result).toEqual({
        status: true,
        contactCount: 0,
        successfulRecords: 0,
        totalRecords: 0,
        message: '24-hour sync ended',
        syncRecords: []
      });

      expect(mockEnhancedLogger.logSyncOperation).toHaveBeenCalledWith(
        'cron',
        'success',
        expect.objectContaining({ contactCount: 0 })
      );
    });
  });

  describe('checkAndManageContacts', () => {
    it('should process contacts successfully', async () => {
      const mockContacts = [
        { id: 1, sf_record_id: 'sf123' },
        { id: 2, sf_record_id: 'sf456' }
      ];

      // Mock Salesforce operations
      mockSalesforceClient.fetchAllRecords.mockResolvedValue([
        { Id: 'sf123', FirstName: 'John', LastName: 'Doe', Email: '<EMAIL>' },
        { Id: 'sf456', FirstName: 'Jane', LastName: 'Smith', Email: '<EMAIL>' }
      ]);

      contactSyncService.updateContacts = jest.fn().mockResolvedValue([
        { status: true, recordId: 'sf123' },
        { status: true, recordId: 'sf456' }
      ]);

      const result = await contactSyncService.checkAndManageContacts(mockContacts);

      expect(result).toEqual([
        { status: true, recordId: 'sf123' },
        { status: true, recordId: 'sf456' }
      ]);

      expect(mockSalesforceClient.fetchAllRecords).toHaveBeenCalledWith(
        'Contact',
        expect.any(String),
        expect.any(Array)
      );
    });

    it('should handle Salesforce fetch errors', async () => {
      const mockContacts = [{ id: 1, sf_record_id: 'sf123' }];
      const error = new Error('Salesforce API error');
      
      mockSalesforceClient.fetchAllRecords.mockRejectedValue(error);

      await expect(contactSyncService.checkAndManageContacts(mockContacts))
        .rejects.toThrow('Salesforce API error');
    });
  });

  describe('updateContacts', () => {
    it('should update contacts in database', async () => {
      const mockSfContacts = [
        { Id: 'sf123', FirstName: 'John', LastName: 'Doe', Email: '<EMAIL>' }
      ];

      mockPool.query.mockResolvedValue({ affectedRows: 1 });

      const result = await contactSyncService.updateContacts(mockSfContacts);

      expect(result).toEqual([
        { status: true, recordId: 'sf123' }
      ]);

      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE contacts SET'),
        expect.any(Array)
      );
    });

    it('should handle database update errors', async () => {
      const mockSfContacts = [
        { Id: 'sf123', FirstName: 'John', LastName: 'Doe', Email: '<EMAIL>' }
      ];

      const error = new Error('Database update failed');
      mockPool.query.mockRejectedValue(error);

      const result = await contactSyncService.updateContacts(mockSfContacts);

      expect(result).toEqual([
        { status: false, recordId: 'sf123', error: 'Database update failed' }
      ]);
    });
  });

  describe('deleteDetails', () => {
    it('should process deleted records', async () => {
      const mockDeletedRecords = [
        { Id: 'sf789', isDeleted: true }
      ];

      mockSalesforceClient.getDeletedData.mockResolvedValue(mockDeletedRecords);
      mockPool.query.mockResolvedValue({ affectedRows: 1 });

      const result = await contactSyncService.deleteDetails();

      expect(result).toEqual({
        execute: true,
        status: true,
        deletedCount: 1
      });

      expect(mockSalesforceClient.getDeletedData).toHaveBeenCalledWith(
        'Contact',
        expect.any(String),
        expect.any(String)
      );
    });

    it('should handle no deleted records', async () => {
      mockSalesforceClient.getDeletedData.mockResolvedValue([]);

      const result = await contactSyncService.deleteDetails();

      expect(result).toEqual({
        execute: false,
        status: true,
        deletedCount: 0
      });
    });
  });
});
