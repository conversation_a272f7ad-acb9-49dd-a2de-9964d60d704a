const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const ElasticSearch = require('../clients/elastic-search');
const { getCurrentAtlanticTime } = require('../helper/custom-helper');
const CONFIG = require('../config');

class EnhancedLogger {
    constructor() {
        this.elasticSearch = new ElasticSearch();
        this.logDir = '/tmp/webhook';
        this.setupWinstonLogger();
    }

    setupWinstonLogger() {
        // Ensure log directory exists
        const fs = require('fs');
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }

        // Define common log format
        const commonFormat = winston.format.combine(
            winston.format.timestamp({
                format: 'YYYY-MM-DD HH:mm:ss',
            }),
            winston.format.errors({ stack: true }),
            winston.format.json()
        );

        // Console format for development
        const consoleFormat = winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ timestamp, level, message, ...meta }) => {
                return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
            })
        );

        // Common transports
        const transports = [
            new winston.transports.Console({
                format: CONFIG.NODE_ENV === 'local' ? consoleFormat : commonFormat,
                level: CONFIG.NODE_ENV === 'local' ? 'debug' : 'info'
            }),
            new DailyRotateFile({
                filename: `${this.logDir}/%DATE%-combined.log`,
                datePattern: 'YYYY-MM-DD',
                maxFiles: '14d',
                maxSize: '20m',
                format: commonFormat
            }),
            new DailyRotateFile({
                filename: `${this.logDir}/%DATE%-error.log`,
                datePattern: 'YYYY-MM-DD',
                level: 'error',
                maxFiles: '30d',
                maxSize: '20m',
                format: commonFormat
            })
        ];

        this.logger = winston.createLogger({
            level: 'info',
            format: commonFormat,
            transports,
            exitOnError: false,
        });
    }

    async logToElastic(index, logData, level = 'info') {
        try {
            const elasticLogData = {
                ...logData,
                'log.level': level.toUpperCase(),
                timestamp: getCurrentAtlanticTime(null, "elasticSearch"),
                application: 'webhook',
                environment: CONFIG.NODE_ENV || 'unknown'
            };

            const result = await this.elasticSearch.insertDocument(index, elasticLogData);
            return result;
        } catch (error) {
            this.logger.error('Failed to log to Elasticsearch', { error: error.message, logData });
            return null;
        }
    }

    // Enhanced logging methods
    async info(message, meta = {}, elasticIndex = null) {
        const logData = { message, ...meta };
        this.logger.info(message, meta);
        
        if (elasticIndex) {
            return await this.logToElastic(elasticIndex, logData, 'info');
        }
        return null;
    }

    async error(message, meta = {}, elasticIndex = null) {
        const logData = { message, ...meta };
        this.logger.error(message, meta);
        
        if (elasticIndex) {
            return await this.logToElastic(elasticIndex, logData, 'error');
        }
        return null;
    }

    async warn(message, meta = {}, elasticIndex = null) {
        const logData = { message, ...meta };
        this.logger.warn(message, meta);
        
        if (elasticIndex) {
            return await this.logToElastic(elasticIndex, logData, 'warn');
        }
        return null;
    }

    async debug(message, meta = {}, elasticIndex = null) {
        const logData = { message, ...meta };
        this.logger.debug(message, meta);
        
        if (elasticIndex) {
            return await this.logToElastic(elasticIndex, logData, 'debug');
        }
        return null;
    }

    // Webhook-specific logging methods
    async logWebhookReceived(req, organizationId, sfApiName, meta = {}) {
        const logData = {
            organizationId,
            sfApiName,
            method: req.method,
            url: req.url,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            contentType: req.get('Content-Type'),
            contentLength: req.get('Content-Length'),
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('webhook_received_logs', logData, 'info');
        this.logger.info(`Webhook received: ${sfApiName} from ${organizationId}`, logData);
        
        return elasticResult;
    }

    async logWebhookProcessing(sfApiName, webhookData, processingStep, meta = {}) {
        const logData = {
            sfApiName,
            processingStep,
            recordId: webhookData?.Id,
            recordName: webhookData?.Name,
            lastModifiedDate: webhookData?.LastModifiedDate,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('webhook_processing_logs', logData, 'info');
        this.logger.info(`Webhook processing: ${sfApiName} - ${processingStep}`, logData);
        
        return elasticResult;
    }

    async logWebhookError(sfApiName, error, webhookData = {}, meta = {}) {
        const logData = {
            sfApiName,
            error: error.message,
            stack: error.stack,
            recordId: webhookData?.Id,
            recordName: webhookData?.Name,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('webhook_error_logs', logData, 'error');
        this.logger.error(`Webhook error: ${sfApiName} - ${error.message}`, logData);
        
        return elasticResult;
    }

    async logDataTransformation(operation, inputData, outputData, meta = {}) {
        const logData = {
            operation,
            inputDataKeys: Object.keys(inputData || {}),
            outputDataKeys: Object.keys(outputData || {}),
            inputRecordCount: Array.isArray(inputData) ? inputData.length : 1,
            outputRecordCount: Array.isArray(outputData) ? outputData.length : 1,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('data_transformation_logs', logData, 'info');
        this.logger.info(`Data transformation: ${operation}`, logData);
        
        return elasticResult;
    }

    async logDatabaseOperation(operation, table, meta = {}) {
        const logData = {
            operation,
            table,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('database_logs', logData, 'info');
        this.logger.info(`Database operation: ${operation} on ${table}`, logData);
        
        return elasticResult;
    }

    async logSalesforceOperation(operation, objectType, meta = {}) {
        const logData = {
            operation,
            objectType,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('salesforce_logs', logData, 'info');
        this.logger.info(`Salesforce operation: ${operation} on ${objectType}`, logData);
        
        return elasticResult;
    }

    async logXmlParsing(success, xmlSize, parsedObjectType, meta = {}) {
        const logData = {
            success,
            xmlSize: xmlSize ? `${xmlSize} bytes` : 'unknown',
            parsedObjectType,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const level = success ? 'info' : 'error';
        const elasticResult = await this.logToElastic('xml_parsing_logs', logData, level);
        this.logger[level](`XML parsing ${success ? 'successful' : 'failed'}: ${parsedObjectType}`, logData);
        
        return elasticResult;
    }

    async logAuthentication(success, organizationId, meta = {}) {
        const logData = {
            success,
            organizationId,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const level = success ? 'info' : 'warn';
        const elasticResult = await this.logToElastic('authentication_logs', logData, level);
        this.logger[level](`Authentication ${success ? 'successful' : 'failed'}: ${organizationId}`, logData);
        
        return elasticResult;
    }

    // Performance monitoring
    createTimer(name) {
        const startTime = Date.now();
        return {
            end: async (meta = {}) => {
                const duration = Date.now() - startTime;
                const logData = {
                    operationName: name,
                    duration: `${duration}ms`,
                    timestamp: getCurrentAtlanticTime(),
                    ...meta
                };
                
                const elasticResult = await this.logToElastic('performance_logs', logData, 'info');
                this.logger.info(`Performance: ${name} completed in ${duration}ms`, logData);
                
                return { duration, elasticResult };
            }
        };
    }
}

module.exports = EnhancedLogger;
