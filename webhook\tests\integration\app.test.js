const request = require('supertest');
const express = require('express');
const { mockEnhancedLogger, mockHelpers, sampleXmlData, createMockWebhookService } = require('../mocks');

// Mock all dependencies before requiring the app
jest.mock('../../utils/enhanced-logger');
jest.mock('../../middleware/logging-middleware');
jest.mock('../../helper/custom-helper');
jest.mock('../../services/webhook-api-services');
jest.mock('../../db', () => ({}));
jest.mock('../../config', () => ({
  ORGANIZATIONID: '00DSv000000nGA5',
  NODE_ENV: 'test',
  RESGISTRATION_USERNAME: 'testuser',
  RESGISTRATION_PASSWORD: 'testpass'
}));

const LoggingMiddleware = require('../../middleware/logging-middleware');
const { decryptXML } = require('../../helper/custom-helper');
const WebhookApiService = require('../../services/webhook-api-services');

describe('Webhook Application Integration Tests', () => {
  let app;

  beforeEach(() => {
    // Mock logging middleware
    LoggingMiddleware.mockImplementation(() => ({
      requestLogger: () => (req, res, next) => {
        req.logData = { requestId: 'webhook-test-request-id', startTime: Date.now() };
        next();
      },
      responseLogger: () => (req, res, next) => next(),
      errorLogger: () => (err, req, res, next) => next(err)
    }));

    // Setup helper mocks
    decryptXML.mockImplementation(mockHelpers.decryptXML);

    // Setup service mocks
    const mockWebhookService = createMockWebhookService();
    WebhookApiService.mockImplementation(() => mockWebhookService);

    // Create a fresh app instance for each test
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Add mock logging middleware
    const loggingMiddleware = new LoggingMiddleware();
    app.use(loggingMiddleware.requestLogger());
    app.use(loggingMiddleware.responseLogger());

    // Add routes
    require('../../routes/webhook-route')(app);
    
    // Add error handling
    app.use(loggingMiddleware.errorLogger());
    app.use((err, req, res, next) => {
      res.status(500).json({ error: err.message });
    });
  });

  describe('Health Check', () => {
    it('should respond to root endpoint', async () => {
      app.get('/', (req, res) => {
        res.status(200).send({ status: 200 });
      });

      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toEqual({ status: 200 });
    });
  });

  describe('Webhook Route Integration', () => {
    it('should process valid webhook requests end-to-end', async () => {
      decryptXML.mockResolvedValue({
        status: true,
        webhookData: { Id: 'test-id', Name: 'Test Contact' },
        sfApiName: 'Contact',
        organizationId: '00DSv000000nGA5MAM',
        sessionId: 'test-session'
      });

      const response = await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>true</status>');
      expect(response.headers['content-type']).toContain('application/xml');
    });

    it('should handle authentication failures', async () => {
      decryptXML.mockResolvedValue({
        status: true,
        webhookData: { Id: 'test-id', Name: 'Test Contact' },
        sfApiName: 'Contact',
        organizationId: 'INVALID_ORG_ID',
        sessionId: 'test-session'
      });

      const response = await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>false</status>');
    });

    it('should handle malformed XML gracefully', async () => {
      decryptXML.mockRejectedValue(new Error('XML parsing failed'));

      const response = await request(app)
        .post('/webhook/upsert')
        .send('invalid xml data')
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>false</status>');
    });
  });

  describe('Sync Endpoints Integration', () => {
    beforeEach(() => {
      // Add sync endpoints to the app
      const checkAuthorization = (req) => {
        const authHeader = req.headers['authorization'];
        if (!authHeader || !authHeader.startsWith('Basic ')) {
          throw new Error('Unauthorized access');
        }
        const base64Credentials = authHeader.split(' ')[1];
        const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
        const [username, password] = credentials.split(':');
        if (username !== 'testuser' || password !== 'testpass') {
          throw new Error('Unauthorized access');
        }
      };

      app.get('/get-contacts-and-sync', async (req, res, next) => {
        try {
          checkAuthorization(req);
          const { email } = req.query;
          // Mock successful sync
          res.send({ message: 'Success', email, contactsProcessed: email ? 1 : 100 });
        } catch (error) {
          next(error);
        }
      });

      app.post('/delete-contact', async (req, res, next) => {
        try {
          checkAuthorization(req);
          const { email } = req.body;
          if (!email) throw new Error('Email not found.');
          // Mock successful deletion
          res.send({ message: 'Success', email });
        } catch (error) {
          next(error);
        }
      });
    });

    it('should handle authorized contact sync requests', async () => {
      const credentials = Buffer.from('testuser:testpass').toString('base64');

      const response = await request(app)
        .get('/get-contacts-and-sync')
        .set('Authorization', `Basic ${credentials}`)
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        contactsProcessed: 100
      });
    });

    it('should handle authorized contact sync with email', async () => {
      const credentials = Buffer.from('testuser:testpass').toString('base64');

      const response = await request(app)
        .get('/get-contacts-and-sync')
        .query({ email: '<EMAIL>' })
        .set('Authorization', `Basic ${credentials}`)
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        email: '<EMAIL>',
        contactsProcessed: 1
      });
    });

    it('should handle authorized delete contact requests', async () => {
      const credentials = Buffer.from('testuser:testpass').toString('base64');

      const response = await request(app)
        .post('/delete-contact')
        .set('Authorization', `Basic ${credentials}`)
        .send({ email: '<EMAIL>' })
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        email: '<EMAIL>'
      });
    });

    it('should reject unauthorized sync requests', async () => {
      await request(app)
        .get('/get-contacts-and-sync')
        .expect(500);
    });

    it('should reject requests with invalid credentials', async () => {
      const credentials = Buffer.from('wronguser:wrongpass').toString('base64');

      await request(app)
        .get('/get-contacts-and-sync')
        .set('Authorization', `Basic ${credentials}`)
        .expect(500);
    });

    it('should handle delete contact without email', async () => {
      const credentials = Buffer.from('testuser:testpass').toString('base64');

      await request(app)
        .post('/delete-contact')
        .set('Authorization', `Basic ${credentials}`)
        .send({})
        .expect(500);
    });
  });

  describe('Middleware Integration', () => {
    it('should add request logging data to all requests', async () => {
      app.get('/test-middleware', (req, res) => {
        res.json({ 
          hasLogData: !!req.logData,
          requestId: req.logData?.requestId
        });
      });

      const response = await request(app)
        .get('/test-middleware')
        .expect(200);

      expect(response.body.hasLogData).toBe(true);
      expect(response.body.requestId).toBe('webhook-test-request-id');
    });

    it('should handle XML content type', async () => {
      decryptXML.mockResolvedValue({
        status: true,
        webhookData: { Id: 'test-id' },
        sfApiName: 'Contact',
        organizationId: '00DSv000000nGA5MAM',
        sessionId: 'test-session'
      });

      const response = await request(app)
        .post('/webhook/upsert')
        .send('<xml>test</xml>')
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.headers['content-type']).toContain('application/xml');
    });

    it('should handle large payloads', async () => {
      const largeXml = '<xml>' + 'x'.repeat(1000000) + '</xml>'; // 1MB+ payload
      
      decryptXML.mockResolvedValue({
        status: true,
        webhookData: { Id: 'test-id' },
        sfApiName: 'Contact',
        organizationId: '00DSv000000nGA5MAM',
        sessionId: 'test-session'
      });

      const response = await request(app)
        .post('/webhook/upsert')
        .send(largeXml)
        .set('Content-Type', 'application/xml')
        .expect(200);

      expect(response.text).toContain('<status>true</status>');
    });
  });

  describe('Performance and Monitoring', () => {
    it('should handle concurrent webhook requests', async () => {
      decryptXML.mockResolvedValue({
        status: true,
        webhookData: { Id: 'test-id' },
        sfApiName: 'Contact',
        organizationId: '00DSv000000nGA5MAM',
        sessionId: 'test-session'
      });

      const requests = Array(10).fill().map(() => 
        request(app)
          .post('/webhook/upsert')
          .send(sampleXmlData.contact)
          .set('Content-Type', 'application/xml')
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.text).toContain('<status>true</status>');
      });
    });

    it('should track request processing time', async () => {
      const startTime = Date.now();
      
      decryptXML.mockImplementation(async () => {
        await testUtils.wait(50); // Simulate processing time
        return {
          status: true,
          webhookData: { Id: 'test-id' },
          sfApiName: 'Contact',
          organizationId: '00DSv000000nGA5MAM',
          sessionId: 'test-session'
        };
      });

      await request(app)
        .post('/webhook/upsert')
        .send(sampleXmlData.contact)
        .set('Content-Type', 'application/xml')
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeGreaterThan(50);
    });
  });
});
