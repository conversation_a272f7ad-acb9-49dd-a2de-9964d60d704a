const request = require('supertest');
const express = require('express');
const SubscriptionController = require('../../controllers/subscription-controller');
const { mockResponses, createMockService, mockMiddleware } = require('../mocks');

// Mock dependencies
jest.mock('../../services/subscription-services');
jest.mock('../../middleware/userAuth');
jest.mock('../../middleware/validationMid');
jest.mock('../../utils/logger');

const SubscriptionServices = require('../../services/subscription-services');
const { authenticateToken } = require('../../middleware/userAuth');
const { validator } = require('../../middleware/validationMid');

describe('SubscriptionController', () => {
  let app;
  let mockSubscriptionService;

  beforeEach(() => {
    // Setup Express app for testing
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Setup middleware mocks
    authenticateToken.mockImplementation(mockMiddleware.authenticateToken);
    validator.mockImplementation(() => mockMiddleware.validator());

    // Setup service mocks
    mockSubscriptionService = createMockService({
      getRenewalEstimate: jest.fn(),
      renewalBillingDate: jest.fn(),
      getEstimateForInternetUpdate: jest.fn(),
      updateInternet: jest.fn(),
      getEstimateForTelevisionUpdate: jest.fn(),
      updateTelevision: jest.fn(),
      getEstimateForupdatePhone: jest.fn(),
      updatePhone: jest.fn(),
      cancelAddonsSubscription: jest.fn()
    });
    SubscriptionServices.mockImplementation(() => mockSubscriptionService);

    // Setup routes
    app.get('/api/v1/subscription/renewal-estimate', SubscriptionController.getRenewalEstimate);
    app.get('/api/v1/subscription/renewal-billing-date', SubscriptionController.renewalBillingDate);
    app.get('/api/v1/subscription/internet-estimate', SubscriptionController.getEstimateForInternetUpdate);
    app.put('/api/v1/subscription/internet', SubscriptionController.updateInternet);
    app.get('/api/v1/subscription/television-estimate', SubscriptionController.getEstimateForTelevisionUpdate);
    app.put('/api/v1/subscription/television', SubscriptionController.updateTelevision);
    app.get('/api/v1/subscription/phone-estimate', SubscriptionController.getEstimateForupdatePhone);
    app.put('/api/v1/subscription/phone', SubscriptionController.updatePhone);
    app.delete('/api/v1/subscription/addons/:subscriptionId', SubscriptionController.cancelAddonsSubscription);
  });

  describe('GET /api/v1/subscription/renewal-estimate', () => {
    it('should successfully get renewal estimate', async () => {
      const renewalEstimateResult = {
        status: true,
        data: {
          nextBillingDate: '2024-02-01',
          amount: 99.99,
          currency: 'USD',
          items: [
            { name: 'Internet Plan', amount: 59.99 },
            { name: 'TV Package', amount: 39.99 }
          ]
        }
      };

      mockSubscriptionService.getRenewalEstimate.mockResolvedValue(renewalEstimateResult);

      const response = await request(app)
        .get('/api/v1/subscription/renewal-estimate')
        .query({ subscriptionId: 'test-subscription-id' })
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: renewalEstimateResult.data
      });
      expect(mockSubscriptionService.getRenewalEstimate).toHaveBeenCalledWith(
        'test-user-id',
        expect.objectContaining({ subscriptionId: 'test-subscription-id' }),
        expect.any(Object)
      );
    });

    it('should handle subscription not found', async () => {
      const renewalEstimateResult = {
        status: false,
        message: 'Subscription not found'
      };

      mockSubscriptionService.getRenewalEstimate.mockResolvedValue(renewalEstimateResult);

      await request(app)
        .get('/api/v1/subscription/renewal-estimate')
        .query({ subscriptionId: 'non-existent-id' })
        .expect(400);
    });
  });

  describe('GET /api/v1/subscription/renewal-billing-date', () => {
    it('should successfully get renewal billing date', async () => {
      const billingDateResult = {
        status: true,
        data: {
          nextBillingDate: '2024-02-01',
          billingCycle: 'monthly',
          daysUntilRenewal: 15
        }
      };

      mockSubscriptionService.renewalBillingDate.mockResolvedValue(billingDateResult);

      const response = await request(app)
        .get('/api/v1/subscription/renewal-billing-date')
        .query({ subscriptionId: 'test-subscription-id' })
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: billingDateResult.data
      });
    });
  });

  describe('GET /api/v1/subscription/internet-estimate', () => {
    it('should successfully get internet update estimate', async () => {
      const internetEstimateResult = {
        status: true,
        data: {
          currentPlan: 'Basic Internet',
          newPlan: 'Premium Internet',
          priceDifference: 20.00,
          effectiveDate: '2024-02-01'
        }
      };

      mockSubscriptionService.getEstimateForInternetUpdate.mockResolvedValue(internetEstimateResult);

      const response = await request(app)
        .get('/api/v1/subscription/internet-estimate')
        .query({ 
          subscriptionId: 'test-subscription-id',
          newPlanId: 'premium-internet-plan'
        })
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: internetEstimateResult.data
      });
    });
  });

  describe('PUT /api/v1/subscription/internet', () => {
    it('should successfully update internet subscription', async () => {
      const updateData = {
        subscriptionId: 'test-subscription-id',
        newPlanId: 'premium-internet-plan',
        effectiveDate: '2024-02-01'
      };

      const updateResult = {
        status: true,
        data: {
          subscriptionId: 'test-subscription-id',
          message: 'Internet plan updated successfully'
        }
      };

      mockSubscriptionService.updateInternet.mockResolvedValue(updateResult);

      const response = await request(app)
        .put('/api/v1/subscription/internet')
        .send(updateData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: updateResult.data
      });
      expect(mockSubscriptionService.updateInternet).toHaveBeenCalledWith(
        'test-user-id',
        updateData,
        expect.any(Object)
      );
    });

    it('should handle invalid plan update', async () => {
      const updateData = {
        subscriptionId: 'test-subscription-id',
        newPlanId: 'invalid-plan-id'
      };

      const updateResult = {
        status: false,
        message: 'Invalid plan selected'
      };

      mockSubscriptionService.updateInternet.mockResolvedValue(updateResult);

      await request(app)
        .put('/api/v1/subscription/internet')
        .send(updateData)
        .expect(400);
    });
  });

  describe('GET /api/v1/subscription/television-estimate', () => {
    it('should successfully get television update estimate', async () => {
      const televisionEstimateResult = {
        status: true,
        data: {
          currentPackage: 'Basic TV',
          newPackage: 'Premium TV',
          priceDifference: 15.00,
          additionalChannels: 50
        }
      };

      mockSubscriptionService.getEstimateForTelevisionUpdate.mockResolvedValue(televisionEstimateResult);

      const response = await request(app)
        .get('/api/v1/subscription/television-estimate')
        .query({ 
          subscriptionId: 'test-subscription-id',
          newPackageId: 'premium-tv-package'
        })
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: televisionEstimateResult.data
      });
    });
  });

  describe('PUT /api/v1/subscription/television', () => {
    it('should successfully update television subscription', async () => {
      const updateData = {
        subscriptionId: 'test-subscription-id',
        newPackageId: 'premium-tv-package',
        addons: ['sports-package', 'movie-package']
      };

      const updateResult = {
        status: true,
        data: {
          subscriptionId: 'test-subscription-id',
          message: 'Television package updated successfully'
        }
      };

      mockSubscriptionService.updateTelevision.mockResolvedValue(updateResult);

      const response = await request(app)
        .put('/api/v1/subscription/television')
        .send(updateData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: updateResult.data
      });
    });
  });

  describe('GET /api/v1/subscription/phone-estimate', () => {
    it('should successfully get phone update estimate', async () => {
      const phoneEstimateResult = {
        status: true,
        data: {
          currentPlan: 'Basic Phone',
          newPlan: 'Unlimited Phone',
          priceDifference: 10.00,
          features: ['Unlimited calling', 'Voicemail']
        }
      };

      mockSubscriptionService.getEstimateForupdatePhone.mockResolvedValue(phoneEstimateResult);

      const response = await request(app)
        .get('/api/v1/subscription/phone-estimate')
        .query({ 
          subscriptionId: 'test-subscription-id',
          newPlanId: 'unlimited-phone-plan'
        })
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: phoneEstimateResult.data
      });
    });
  });

  describe('PUT /api/v1/subscription/phone', () => {
    it('should successfully update phone subscription', async () => {
      const updateData = {
        subscriptionId: 'test-subscription-id',
        newPlanId: 'unlimited-phone-plan'
      };

      const updateResult = {
        status: true,
        data: {
          subscriptionId: 'test-subscription-id',
          message: 'Phone plan updated successfully'
        }
      };

      mockSubscriptionService.updatePhone.mockResolvedValue(updateResult);

      const response = await request(app)
        .put('/api/v1/subscription/phone')
        .send(updateData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: updateResult.data
      });
    });
  });

  describe('DELETE /api/v1/subscription/addons/:subscriptionId', () => {
    it('should successfully cancel addon subscription', async () => {
      const subscriptionId = 'test-addon-subscription-id';

      const cancelResult = {
        status: true,
        data: {
          subscriptionId,
          message: 'Addon subscription cancelled successfully'
        }
      };

      mockSubscriptionService.cancelAddonsSubscription.mockResolvedValue(cancelResult);

      const response = await request(app)
        .delete(`/api/v1/subscription/addons/${subscriptionId}`)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: cancelResult.data
      });
      expect(mockSubscriptionService.cancelAddonsSubscription).toHaveBeenCalledWith(
        'test-user-id',
        subscriptionId,
        expect.any(Object)
      );
    });

    it('should handle addon not found', async () => {
      const subscriptionId = 'non-existent-addon-id';

      const cancelResult = {
        status: false,
        message: 'Addon subscription not found'
      };

      mockSubscriptionService.cancelAddonsSubscription.mockResolvedValue(cancelResult);

      await request(app)
        .delete(`/api/v1/subscription/addons/${subscriptionId}`)
        .expect(400);
    });
  });

  describe('Error Handling', () => {
    it('should handle service exceptions gracefully', async () => {
      mockSubscriptionService.getRenewalEstimate.mockRejectedValue(new Error('Chargebee API error'));

      await request(app)
        .get('/api/v1/subscription/renewal-estimate')
        .query({ subscriptionId: 'test-subscription-id' })
        .expect(500);
    });
  });
});
