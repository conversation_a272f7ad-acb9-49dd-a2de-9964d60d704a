const request = require('supertest');
const express = require('express');
const CustomerController = require('../../controllers/customer-controller');
const { mockResponses, createMockService, mockMiddleware } = require('../mocks');

// Mock dependencies
jest.mock('../../services/customer-services');
jest.mock('../../middleware/userAuth');
jest.mock('../../middleware/validationMid');
jest.mock('../../utils/logger');
jest.mock('multer', () => jest.fn(() => ({
  single: jest.fn(() => (req, res, next) => {
    req.file = {
      fieldname: 'image_url',
      originalname: 'test-image.jpg',
      mimetype: 'image/jpeg',
      buffer: Buffer.from('test-image-data'),
      size: 1024
    };
    next();
  })
})));

const CustomerServices = require('../../services/customer-services');
const { authenticateToken } = require('../../middleware/userAuth');
const { validator } = require('../../middleware/validationMid');

describe('CustomerController', () => {
  let app;
  let mockCustomerService;

  beforeEach(() => {
    // Setup Express app for testing
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Setup middleware mocks
    authenticateToken.mockImplementation(mockMiddleware.authenticateToken);
    validator.mockImplementation(() => mockMiddleware.validator());

    // Setup service mocks
    mockCustomerService = createMockService({
      getCustomerDetails: jest.fn(),
      updateCustomerDetails: jest.fn(),
      resetPassword: jest.fn(),
      logout: jest.fn()
    });
    CustomerServices.mockImplementation(() => mockCustomerService);

    // Setup routes
    app.get('/api/v1/customer/details', CustomerController.getCustomerDetails);
    app.put('/api/v1/customer', CustomerController.updateCustomerDetails);
    app.post('/api/v1/customer/reset-password', CustomerController.resetPassword);
    app.get('/api/v1/customer/logout', CustomerController.logout);
  });

  describe('GET /api/v1/customer/details', () => {
    it('should successfully get customer details', async () => {
      const customerDetailsResult = {
        status: true,
        data: mockResponses.customer
      };

      mockCustomerService.getCustomerDetails.mockResolvedValue(customerDetailsResult);

      const response = await request(app)
        .get('/api/v1/customer/details')
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: customerDetailsResult.data
      });
      expect(mockCustomerService.getCustomerDetails).toHaveBeenCalledWith(
        'test-user-id',
        expect.any(Object)
      );
    });

    it('should handle customer not found', async () => {
      const customerDetailsResult = {
        status: false,
        message: 'Customer not found'
      };

      mockCustomerService.getCustomerDetails.mockResolvedValue(customerDetailsResult);

      await request(app)
        .get('/api/v1/customer/details')
        .expect(400);
    });

    it('should handle service errors', async () => {
      mockCustomerService.getCustomerDetails.mockRejectedValue(new Error('Service error'));

      await request(app)
        .get('/api/v1/customer/details')
        .expect(500);
    });
  });

  describe('PUT /api/v1/customer', () => {
    it('should successfully update customer details', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'User',
        phone: '555-5678'
      };

      const updateResult = {
        status: true,
        data: {
          ...mockResponses.customer,
          firstName: 'Updated',
          lastName: 'User',
          phone: '555-5678'
        }
      };

      mockCustomerService.updateCustomerDetails.mockResolvedValue(updateResult);

      const response = await request(app)
        .put('/api/v1/customer')
        .send(updateData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: updateResult.data
      });
      expect(mockCustomerService.updateCustomerDetails).toHaveBeenCalledWith(
        'test-user-id',
        expect.objectContaining(updateData),
        expect.any(Object)
      );
    });

    it('should handle update failure', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'User',
        phone: 'invalid-phone'
      };

      const updateResult = {
        status: false,
        message: 'Invalid phone number'
      };

      mockCustomerService.updateCustomerDetails.mockResolvedValue(updateResult);

      await request(app)
        .put('/api/v1/customer')
        .send(updateData)
        .expect(400);
    });

    it('should handle image upload', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'User'
      };

      const updateResult = {
        status: true,
        data: {
          ...mockResponses.customer,
          firstName: 'Updated',
          lastName: 'User',
          imageUrl: 'https://example.com/images/test-image.jpg'
        }
      };

      mockCustomerService.updateCustomerDetails.mockResolvedValue(updateResult);

      const response = await request(app)
        .put('/api/v1/customer')
        .field('firstName', 'Updated')
        .field('lastName', 'User')
        .attach('image_url', Buffer.from('test-image-data'), 'test-image.jpg')
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: updateResult.data
      });
    });
  });

  describe('POST /api/v1/customer/reset-password', () => {
    it('should successfully reset password', async () => {
      const resetPasswordData = {
        oldPassword: 'oldpassword123',
        newPassword: 'newpassword123'
      };

      const resetPasswordResult = {
        status: true,
        data: { message: 'Password reset successful' }
      };

      mockCustomerService.resetPassword.mockResolvedValue(resetPasswordResult);

      const response = await request(app)
        .post('/api/v1/customer/reset-password')
        .send(resetPasswordData)
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: resetPasswordResult.data
      });
      expect(mockCustomerService.resetPassword).toHaveBeenCalledWith(
        'test-user-id',
        resetPasswordData.oldPassword,
        resetPasswordData.newPassword,
        expect.any(Object)
      );
    });

    it('should handle incorrect old password', async () => {
      const resetPasswordData = {
        oldPassword: 'wrongpassword',
        newPassword: 'newpassword123'
      };

      const resetPasswordResult = {
        status: false,
        message: 'Incorrect old password'
      };

      mockCustomerService.resetPassword.mockResolvedValue(resetPasswordResult);

      await request(app)
        .post('/api/v1/customer/reset-password')
        .send(resetPasswordData)
        .expect(400);
    });
  });

  describe('GET /api/v1/customer/logout', () => {
    it('should successfully logout user', async () => {
      const logoutResult = {
        status: true,
        data: { message: 'Logged out successfully' }
      };

      mockCustomerService.logout.mockResolvedValue(logoutResult);

      const response = await request(app)
        .get('/api/v1/customer/logout')
        .expect(200);

      expect(response.body).toEqual({
        status: 200,
        message: 'Success',
        data: logoutResult.data
      });
      expect(mockCustomerService.logout).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Object)
      );
    });

    it('should handle logout failure', async () => {
      const logoutResult = {
        status: false,
        message: 'Logout failed'
      };

      mockCustomerService.logout.mockResolvedValue(logoutResult);

      await request(app)
        .get('/api/v1/customer/logout')
        .expect(400);
    });
  });

  describe('Error Handling', () => {
    it('should handle service exceptions gracefully', async () => {
      mockCustomerService.getCustomerDetails.mockRejectedValue(new Error('Database connection failed'));

      await request(app)
        .get('/api/v1/customer/details')
        .expect(500);
    });

    it('should handle validation errors', async () => {
      // This would be handled by validation middleware in real scenario
      await request(app)
        .post('/api/v1/customer/reset-password')
        .send({ oldPassword: 'oldpassword123' }) // Missing newPassword
        .expect(500);
    });
  });
});
