const request = require('supertest');
const express = require('express');
const { mockEnhancedLogger, createMockService } = require('../mocks');

// Mock all dependencies before requiring the app
jest.mock('../../utils/enhanced-logger');
jest.mock('../../middleware/logging-middleware');
jest.mock('../../services/process-services/contact-sync-services');
jest.mock('../../services/delete-contact-service');
jest.mock('../../services/process-services/order-services');
jest.mock('../../services/chargebee/invoice-sync-services');
jest.mock('../../services/cron-services');
jest.mock('../../cron/cron', () => jest.fn());
jest.mock('../../db', () => ({}));

const LoggingMiddleware = require('../../middleware/logging-middleware');

describe('Salesforce Sync Application Integration Tests', () => {
  let app;

  beforeEach(() => {
    // Mock logging middleware
    LoggingMiddleware.mockImplementation(() => ({
      requestLogger: () => (req, res, next) => {
        req.logData = { requestId: 'test-request-id', startTime: Date.now() };
        next();
      },
      responseLogger: () => (req, res, next) => next(),
      errorLogger: () => (err, req, res, next) => next(err)
    }));

    // Create a fresh app instance for each test
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Add mock logging middleware
    const loggingMiddleware = new LoggingMiddleware();
    app.use(loggingMiddleware.requestLogger());
    app.use(loggingMiddleware.responseLogger());

    // Add routes
    require('../../routes/sync-route')(app);
    
    // Add error handling
    app.use(loggingMiddleware.errorLogger());
    app.use((err, req, res, next) => {
      res.status(500).json({ error: err.message });
    });
  });

  describe('Health Check', () => {
    it('should respond to root endpoint', async () => {
      app.get('/', (req, res) => {
        res.status(200).send({ message: 'SYNC PROCESS RUNNING ON PORT 4000' });
      });

      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toEqual({
        message: 'SYNC PROCESS RUNNING ON PORT 4000'
      });
    });
  });

  describe('API Routes Integration', () => {
    it('should handle contact sync requests', async () => {
      const ContactTestService = require('../../services/process-services/contact-sync-services');
      const mockService = createMockService({
        getContactDetails: jest.fn().mockResolvedValue({
          status: true,
          contactCount: 5,
          message: 'Sync completed'
        })
      });
      ContactTestService.mockImplementation(() => mockService);

      const response = await request(app)
        .get('/get-contacts-and-sync')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        contactsProcessed: 5,
        status: true
      });
    });

    it('should handle delete contact requests', async () => {
      const DeleteContactService = require('../../services/delete-contact-service');
      const mockService = createMockService({
        deleteContact: jest.fn().mockResolvedValue({ success: true })
      });
      DeleteContactService.mockImplementation(() => mockService);

      const response = await request(app)
        .post('/delete-contact')
        .send({ email: '<EMAIL>' })
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        email: '<EMAIL>'
      });
    });

    it('should handle order sync requests', async () => {
      const OrderTestServices = require('../../services/process-services/order-services');
      const mockService = createMockService({
        getCreationOrderDetails: jest.fn().mockResolvedValue({
          status: true,
          orderCount: 3
        })
      });
      OrderTestServices.mockImplementation(() => mockService);

      const response = await request(app)
        .get('/get-orders-and-sync')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        ordersProcessed: 3,
        status: true
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle service errors gracefully', async () => {
      const ContactTestService = require('../../services/process-services/contact-sync-services');
      const mockService = createMockService({
        getContactDetails: jest.fn().mockRejectedValue(new Error('Service unavailable'))
      });
      ContactTestService.mockImplementation(() => mockService);

      await request(app)
        .get('/get-contacts-and-sync')
        .expect(500);
    });

    it('should handle validation errors', async () => {
      const DeleteContactService = require('../../services/delete-contact-service');
      const mockService = createMockService({
        deleteContact: jest.fn().mockRejectedValue(new Error('Email not found'))
      });
      DeleteContactService.mockImplementation(() => mockService);

      await request(app)
        .post('/delete-contact')
        .send({}) // No email provided
        .expect(500);
    });
  });

  describe('Middleware Integration', () => {
    it('should add request logging data to all requests', async () => {
      app.get('/test-middleware', (req, res) => {
        res.json({ 
          hasLogData: !!req.logData,
          requestId: req.logData?.requestId
        });
      });

      const response = await request(app)
        .get('/test-middleware')
        .expect(200);

      expect(response.body.hasLogData).toBe(true);
      expect(response.body.requestId).toBe('test-request-id');
    });

    it('should handle JSON parsing', async () => {
      const DeleteContactService = require('../../services/delete-contact-service');
      const mockService = createMockService({
        deleteContact: jest.fn().mockResolvedValue({ success: true })
      });
      DeleteContactService.mockImplementation(() => mockService);

      const response = await request(app)
        .post('/delete-contact')
        .send({ email: '<EMAIL>' })
        .set('Content-Type', 'application/json')
        .expect(200);

      expect(response.body.message).toBe('Success');
    });

    it('should handle URL-encoded data', async () => {
      const DeleteContactService = require('../../services/delete-contact-service');
      const mockService = createMockService({
        deleteContact: jest.fn().mockResolvedValue({ success: true })
      });
      DeleteContactService.mockImplementation(() => mockService);

      const response = await request(app)
        .post('/delete-contact')
        .send('email=<EMAIL>')
        .set('Content-Type', 'application/x-www-form-urlencoded')
        .expect(200);

      expect(response.body.message).toBe('Success');
    });
  });

  describe('Performance and Monitoring', () => {
    it('should track request timing', async () => {
      const startTime = Date.now();
      
      const ContactTestService = require('../../services/process-services/contact-sync-services');
      const mockService = createMockService({
        getContactDetails: jest.fn().mockImplementation(async () => {
          await testUtils.wait(50); // Simulate processing time
          return { status: true, contactCount: 1 };
        })
      });
      ContactTestService.mockImplementation(() => mockService);

      await request(app)
        .get('/get-contacts-and-sync')
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeGreaterThan(50);
    });

    it('should handle concurrent requests', async () => {
      const ContactTestService = require('../../services/process-services/contact-sync-services');
      const mockService = createMockService({
        getContactDetails: jest.fn().mockResolvedValue({
          status: true,
          contactCount: 1
        })
      });
      ContactTestService.mockImplementation(() => mockService);

      const requests = Array(5).fill().map(() => 
        request(app).get('/get-contacts-and-sync')
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.message).toBe('Success');
      });
    });
  });
});
