const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const ElasticSearch = require('../clients/elastic-search');
const { getCurrentAtlanticTime } = require('../helper/custom-helper');
const CONFIG = require('../config');

class EnhancedLogger {
    constructor() {
        this.elasticSearch = new ElasticSearch();
        this.logDir = '/tmp/salesforce-sync';
        this.setupWinstonLogger();
    }

    setupWinstonLogger() {
        // Ensure log directory exists
        const fs = require('fs');
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }

        // Define common log format
        const commonFormat = winston.format.combine(
            winston.format.timestamp({
                format: 'YYYY-MM-DD HH:mm:ss',
            }),
            winston.format.errors({ stack: true }),
            winston.format.json()
        );

        // Console format for development
        const consoleFormat = winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ timestamp, level, message, ...meta }) => {
                return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
            })
        );

        // Common transports
        const transports = [
            new winston.transports.Console({
                format: CONFIG.NODE_ENV === 'local' ? consoleFormat : commonFormat,
                level: CONFIG.NODE_ENV === 'local' ? 'debug' : 'info'
            }),
            new DailyRotateFile({
                filename: `${this.logDir}/%DATE%-combined.log`,
                datePattern: 'YYYY-MM-DD',
                maxFiles: '14d',
                maxSize: '20m',
                format: commonFormat
            }),
            new DailyRotateFile({
                filename: `${this.logDir}/%DATE%-error.log`,
                datePattern: 'YYYY-MM-DD',
                level: 'error',
                maxFiles: '30d',
                maxSize: '20m',
                format: commonFormat
            })
        ];

        this.logger = winston.createLogger({
            level: 'info',
            format: commonFormat,
            transports,
            exitOnError: false,
        });
    }

    async logToElastic(index, logData, level = 'info') {
        try {
            const elasticLogData = {
                ...logData,
                'log.level': level.toUpperCase(),
                timestamp: getCurrentAtlanticTime(null, "elasticSearch"),
                application: 'salesforce-sync',
                environment: CONFIG.NODE_ENV || 'unknown'
            };

            const result = await this.elasticSearch.insertDocument(index, elasticLogData);
            return result;
        } catch (error) {
            this.logger.error('Failed to log to Elasticsearch', { error: error.message, logData });
            return null;
        }
    }

    // Enhanced logging methods
    async info(message, meta = {}, elasticIndex = null) {
        const logData = { message, ...meta };
        this.logger.info(message, meta);
        
        if (elasticIndex) {
            return await this.logToElastic(elasticIndex, logData, 'info');
        }
        return null;
    }

    async error(message, meta = {}, elasticIndex = null) {
        const logData = { message, ...meta };
        this.logger.error(message, meta);
        
        if (elasticIndex) {
            return await this.logToElastic(elasticIndex, logData, 'error');
        }
        return null;
    }

    async warn(message, meta = {}, elasticIndex = null) {
        const logData = { message, ...meta };
        this.logger.warn(message, meta);
        
        if (elasticIndex) {
            return await this.logToElastic(elasticIndex, logData, 'warn');
        }
        return null;
    }

    async debug(message, meta = {}, elasticIndex = null) {
        const logData = { message, ...meta };
        this.logger.debug(message, meta);
        
        if (elasticIndex) {
            return await this.logToElastic(elasticIndex, logData, 'debug');
        }
        return null;
    }

    // Specialized logging methods for different operations
    async logCronStart(cronName, meta = {}) {
        const logData = {
            cronName,
            status: 'started',
            startTime: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('cron_logs', logData, 'info');
        this.logger.info(`Cron job started: ${cronName}`, logData);
        
        return elasticResult;
    }

    async logCronEnd(cronName, success, meta = {}) {
        const logData = {
            cronName,
            status: success ? 'completed' : 'failed',
            endTime: getCurrentAtlanticTime(),
            success,
            ...meta
        };
        
        const level = success ? 'info' : 'error';
        const elasticResult = await this.logToElastic('cron_logs', logData, level);
        this.logger[level](`Cron job ${success ? 'completed' : 'failed'}: ${cronName}`, logData);
        
        return elasticResult;
    }

    async logApiRequest(req, meta = {}) {
        const logData = {
            method: req.method,
            url: req.url,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            query: req.query,
            body: req.method !== 'GET' ? req.body : undefined,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('api_request_logs', logData, 'info');
        this.logger.info(`API Request: ${req.method} ${req.url}`, logData);
        
        return elasticResult;
    }

    async logApiResponse(req, res, responseTime, meta = {}) {
        const logData = {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const level = res.statusCode >= 400 ? 'error' : 'info';
        const elasticResult = await this.logToElastic('api_response_logs', logData, level);
        this.logger[level](`API Response: ${req.method} ${req.url} - ${res.statusCode} (${responseTime}ms)`, logData);
        
        return elasticResult;
    }

    async logDatabaseOperation(operation, table, meta = {}) {
        const logData = {
            operation,
            table,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('database_logs', logData, 'info');
        this.logger.info(`Database operation: ${operation} on ${table}`, logData);
        
        return elasticResult;
    }

    async logSalesforceOperation(operation, objectType, meta = {}) {
        const logData = {
            operation,
            objectType,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const elasticResult = await this.logToElastic('salesforce_logs', logData, 'info');
        this.logger.info(`Salesforce operation: ${operation} on ${objectType}`, logData);
        
        return elasticResult;
    }

    async logSyncOperation(syncType, status, meta = {}) {
        const logData = {
            syncType,
            status,
            timestamp: getCurrentAtlanticTime(),
            ...meta
        };
        
        const level = status === 'success' ? 'info' : 'error';
        const elasticResult = await this.logToElastic('sync_logs', logData, level);
        this.logger[level](`Sync operation: ${syncType} - ${status}`, logData);
        
        return elasticResult;
    }

    // Performance monitoring
    createTimer(name) {
        const startTime = Date.now();
        return {
            end: async (meta = {}) => {
                const duration = Date.now() - startTime;
                const logData = {
                    operationName: name,
                    duration: `${duration}ms`,
                    timestamp: getCurrentAtlanticTime(),
                    ...meta
                };
                
                const elasticResult = await this.logToElastic('performance_logs', logData, 'info');
                this.logger.info(`Performance: ${name} completed in ${duration}ms`, logData);
                
                return { duration, elasticResult };
            }
        };
    }
}

module.exports = EnhancedLogger;
