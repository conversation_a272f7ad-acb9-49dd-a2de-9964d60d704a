const CustomerServices = require('../../services/customer-services');
const { mockCognitoClient, mockChargebeeClient, mockSequelize, mockModel, mockResponses } = require('../mocks');

// Mock dependencies
jest.mock('../../clients/cognito/cognito-client');
jest.mock('../../clients/chargebee/chargebee-client');
jest.mock('../../models');
jest.mock('../../utils/logger');
jest.mock('../../helper/custom-helper');

const CognitoClient = require('../../clients/cognito/cognito-client');
const ChargebeeClient = require('../../clients/chargebee/chargebee-client');
const { Customer, Subscription } = require('../../models');
const logger = require('../../utils/logger');
const { getCurrentAtlanticTime } = require('../../helper/custom-helper');

describe('CustomerServices', () => {
  let customerService;
  let mockElasticLogObj;

  beforeEach(() => {
    // Setup mocks
    CognitoClient.mockImplementation(() => mockCognitoClient);
    ChargebeeClient.mockImplementation(() => mockChargebeeClient);
    Customer.mockImplementation(() => mockModel);
    Subscription.mockImplementation(() => mockModel);
    
    getCurrentAtlanticTime.mockReturnValue(new Date().toISOString());

    mockElasticLogObj = {
      requestId: 'test-request-id',
      timestamp: new Date().toISOString()
    };

    customerService = new CustomerServices();
  });

  describe('login', () => {
    it('should successfully login a user', async () => {
      const email = '<EMAIL>';
      const password = 'password123';

      // Mock Cognito authentication
      mockCognitoClient.adminInitiateAuth.mockResolvedValue({
        AuthenticationResult: {
          AccessToken: 'test-access-token',
          RefreshToken: 'test-refresh-token',
          IdToken: 'test-id-token'
        }
      });

      // Mock user details retrieval
      mockCognitoClient.adminGetUser.mockResolvedValue(mockResponses.cognitoUser);

      // Mock customer model
      mockModel.findOne.mockResolvedValue({
        dataValues: mockResponses.customer,
        get: jest.fn((key) => mockResponses.customer[key])
      });

      const result = await customerService.login(email, password, mockElasticLogObj);

      expect(result).toEqual({
        status: true,
        data: {
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          user: mockResponses.customer
        }
      });

      expect(mockCognitoClient.adminInitiateAuth).toHaveBeenCalledWith({
        UserPoolId: expect.any(String),
        ClientId: expect.any(String),
        AuthFlow: 'ADMIN_NO_SRP_AUTH',
        AuthParameters: {
          USERNAME: email,
          PASSWORD: password
        }
      });
    });

    it('should handle invalid credentials', async () => {
      const email = '<EMAIL>';
      const password = 'wrongpassword';

      const error = new Error('Incorrect username or password');
      error.code = 'NotAuthorizedException';
      mockCognitoClient.adminInitiateAuth.mockRejectedValue(error);

      const result = await customerService.login(email, password, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Invalid email or password'
      });
    });

    it('should handle user not confirmed', async () => {
      const email = '<EMAIL>';
      const password = 'password123';

      const error = new Error('User is not confirmed');
      error.code = 'UserNotConfirmedException';
      mockCognitoClient.adminInitiateAuth.mockRejectedValue(error);

      const result = await customerService.login(email, password, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'User account is not confirmed'
      });
    });

    it('should handle service errors', async () => {
      const email = '<EMAIL>';
      const password = 'password123';

      const error = new Error('Service unavailable');
      mockCognitoClient.adminInitiateAuth.mockRejectedValue(error);

      const result = await customerService.login(email, password, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Login failed due to service error'
      });
    });
  });

  describe('getCustomerDetails', () => {
    it('should successfully get customer details', async () => {
      const userId = 'test-user-id';

      // Mock customer model
      mockModel.findOne.mockResolvedValue({
        dataValues: mockResponses.customer,
        get: jest.fn((key) => mockResponses.customer[key])
      });

      // Mock subscription model
      mockModel.findAll.mockResolvedValue([
        {
          dataValues: mockResponses.subscription,
          get: jest.fn((key) => mockResponses.subscription[key])
        }
      ]);

      const result = await customerService.getCustomerDetails(userId, mockElasticLogObj);

      expect(result).toEqual({
        status: true,
        data: {
          customer: mockResponses.customer,
          subscriptions: [mockResponses.subscription]
        }
      });

      expect(mockModel.findOne).toHaveBeenCalledWith({
        where: { cognitoUserId: userId }
      });
    });

    it('should handle customer not found', async () => {
      const userId = 'non-existent-user-id';

      mockModel.findOne.mockResolvedValue(null);

      const result = await customerService.getCustomerDetails(userId, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Customer not found'
      });
    });

    it('should handle database errors', async () => {
      const userId = 'test-user-id';
      const error = new Error('Database connection failed');

      mockModel.findOne.mockRejectedValue(error);

      const result = await customerService.getCustomerDetails(userId, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Failed to retrieve customer details'
      });
    });
  });

  describe('updateCustomerDetails', () => {
    it('should successfully update customer details', async () => {
      const userId = 'test-user-id';
      const updateData = {
        firstName: 'Updated',
        lastName: 'User',
        phone: '555-5678'
      };

      // Mock customer model
      const mockCustomer = {
        dataValues: { ...mockResponses.customer, ...updateData },
        get: jest.fn((key) => ({ ...mockResponses.customer, ...updateData })[key]),
        update: jest.fn().mockResolvedValue(true)
      };

      mockModel.findOne.mockResolvedValue(mockCustomer);

      // Mock Cognito update
      mockCognitoClient.adminUpdateUserAttributes.mockResolvedValue({});

      const result = await customerService.updateCustomerDetails(userId, updateData, mockElasticLogObj);

      expect(result).toEqual({
        status: true,
        data: { ...mockResponses.customer, ...updateData }
      });

      expect(mockCustomer.update).toHaveBeenCalledWith(updateData);
      expect(mockCognitoClient.adminUpdateUserAttributes).toHaveBeenCalledWith({
        UserPoolId: expect.any(String),
        Username: userId,
        UserAttributes: expect.arrayContaining([
          { Name: 'given_name', Value: 'Updated' },
          { Name: 'family_name', Value: 'User' }
        ])
      });
    });

    it('should handle customer not found for update', async () => {
      const userId = 'non-existent-user-id';
      const updateData = { firstName: 'Updated' };

      mockModel.findOne.mockResolvedValue(null);

      const result = await customerService.updateCustomerDetails(userId, updateData, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Customer not found'
      });
    });

    it('should handle update errors', async () => {
      const userId = 'test-user-id';
      const updateData = { firstName: 'Updated' };

      const mockCustomer = {
        update: jest.fn().mockRejectedValue(new Error('Update failed'))
      };

      mockModel.findOne.mockResolvedValue(mockCustomer);

      const result = await customerService.updateCustomerDetails(userId, updateData, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Failed to update customer details'
      });
    });
  });

  describe('resetPassword', () => {
    it('should successfully reset password', async () => {
      const userId = 'test-user-id';
      const oldPassword = 'oldpassword123';
      const newPassword = 'newpassword123';

      // Mock password change
      mockCognitoClient.adminSetUserPassword.mockResolvedValue({});

      const result = await customerService.resetPassword(userId, oldPassword, newPassword, mockElasticLogObj);

      expect(result).toEqual({
        status: true,
        data: { message: 'Password reset successfully' }
      });

      expect(mockCognitoClient.adminSetUserPassword).toHaveBeenCalledWith({
        UserPoolId: expect.any(String),
        Username: userId,
        Password: newPassword,
        Permanent: true
      });
    });

    it('should handle incorrect old password', async () => {
      const userId = 'test-user-id';
      const oldPassword = 'wrongpassword';
      const newPassword = 'newpassword123';

      const error = new Error('Incorrect username or password');
      error.code = 'NotAuthorizedException';
      mockCognitoClient.adminSetUserPassword.mockRejectedValue(error);

      const result = await customerService.resetPassword(userId, oldPassword, newPassword, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Incorrect old password'
      });
    });

    it('should handle password policy violations', async () => {
      const userId = 'test-user-id';
      const oldPassword = 'oldpassword123';
      const newPassword = 'weak';

      const error = new Error('Password does not conform to policy');
      error.code = 'InvalidPasswordException';
      mockCognitoClient.adminSetUserPassword.mockRejectedValue(error);

      const result = await customerService.resetPassword(userId, oldPassword, newPassword, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Password does not meet security requirements'
      });
    });
  });

  describe('forgotPassword', () => {
    it('should successfully initiate forgot password', async () => {
      const email = '<EMAIL>';

      mockCognitoClient.forgotPassword.mockResolvedValue({
        CodeDeliveryDetails: {
          Destination: 't***@example.com',
          DeliveryMedium: 'EMAIL'
        }
      });

      const result = await customerService.forgotPassword(email, mockElasticLogObj);

      expect(result).toEqual({
        status: true,
        data: { message: 'Password reset code sent to email' }
      });

      expect(mockCognitoClient.forgotPassword).toHaveBeenCalledWith({
        ClientId: expect.any(String),
        Username: email
      });
    });

    it('should handle user not found', async () => {
      const email = '<EMAIL>';

      const error = new Error('User does not exist');
      error.code = 'UserNotFoundException';
      mockCognitoClient.forgotPassword.mockRejectedValue(error);

      const result = await customerService.forgotPassword(email, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'User not found'
      });
    });
  });

  describe('logout', () => {
    it('should successfully logout user', async () => {
      const accessToken = 'test-access-token';

      mockCognitoClient.globalSignOut.mockResolvedValue({});

      const result = await customerService.logout(accessToken, mockElasticLogObj);

      expect(result).toEqual({
        status: true,
        data: { message: 'Logged out successfully' }
      });

      expect(mockCognitoClient.globalSignOut).toHaveBeenCalledWith({
        AccessToken: accessToken
      });
    });

    it('should handle logout errors', async () => {
      const accessToken = 'invalid-token';

      const error = new Error('Invalid access token');
      mockCognitoClient.globalSignOut.mockRejectedValue(error);

      const result = await customerService.logout(accessToken, mockElasticLogObj);

      expect(result).toEqual({
        status: false,
        message: 'Logout failed'
      });
    });
  });
});
