const EnhancedLogger = require('../utils/enhanced-logger');

class LoggingMiddleware {
    constructor() {
        this.logger = new EnhancedLogger();
    }

    // Request logging middleware
    requestLogger() {
        return async (req, res, next) => {
            const startTime = Date.now();
            
            // Log the incoming request
            const elasticResult = await this.logger.logApiRequest(req, {
                requestId: this.generateRequestId(),
                startTime: new Date(startTime).toISOString()
            });

            // Store request metadata for response logging
            req.logData = {
                startTime,
                elasticId: elasticResult?._id,
                elasticIndex: elasticResult?._index,
                requestId: elasticResult?.requestId || this.generateRequestId()
            };

            next();
        };
    }

    // Response logging middleware
    responseLogger() {
        return async (req, res, next) => {
            const originalSend = res.send;
            const originalJson = res.json;

            // Override res.send
            res.send = function(data) {
                res.locals.responseData = data;
                return originalSend.call(this, data);
            };

            // Override res.json
            res.json = function(data) {
                res.locals.responseData = data;
                return originalJson.call(this, data);
            };

            // Log response when request finishes
            res.on('finish', async () => {
                if (req.logData) {
                    const responseTime = Date.now() - req.logData.startTime;
                    
                    await this.logger.logApiResponse(req, res, responseTime, {
                        requestId: req.logData.requestId,
                        responseData: this.sanitizeResponseData(res.locals.responseData),
                        elasticRequestId: req.logData.elasticId
                    });
                }
            });

            next();
        };
    }

    // Error logging middleware
    errorLogger() {
        return async (err, req, res, next) => {
            const errorData = {
                error: err.message,
                stack: err.stack,
                requestId: req.logData?.requestId,
                method: req.method,
                url: req.url,
                query: req.query,
                body: req.body,
                statusCode: err.statusCode || 500
            };

            await this.logger.error('API Error occurred', errorData, 'api_error_logs');

            next(err);
        };
    }

    // Utility methods
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    sanitizeResponseData(data) {
        if (!data) return null;
        
        // Convert to string if it's an object, then truncate if too long
        const dataStr = typeof data === 'string' ? data : JSON.stringify(data);
        return dataStr.length > 1000 ? `${dataStr.substring(0, 1000)}...` : dataStr;
    }
}

module.exports = LoggingMiddleware;
