const WebhookApiServices = require('../../services/webhook-api-services');
const { mockPool, mockSalesforceClient, mockElasticsearchClient, mockEnhancedLogger, mockResponses } = require('../mocks');

// Mock dependencies
jest.mock('../../db');
jest.mock('../../clients/salesforce-client');
jest.mock('../../clients/elastic-search');
jest.mock('../../utils/enhanced-logger');
jest.mock('../../helper/custom-helper');

const pool = require('../../db');
const SalesforceClient = require('../../clients/salesforce-client');
const ElasticSearch = require('../../clients/elastic-search');
const EnhancedLogger = require('../../utils/enhanced-logger');
const { getCurrentAtlanticTime, hasValidChanges } = require('../../helper/custom-helper');

describe('WebhookApiServices', () => {
  let webhookApiService;

  beforeEach(() => {
    // Setup mocks
    pool.query = mockPool.query;
    SalesforceClient.mockImplementation(() => mockSalesforceClient);
    ElasticSearch.mockImplementation(() => mockElasticsearchClient);
    EnhancedLogger.mockImplementation(() => mockEnhancedLogger);
    
    getCurrentAtlanticTime.mockReturnValue(new Date().toISOString());
    hasValidChanges.mockReturnValue(true);

    webhookApiService = new WebhookApiServices();
  });

  describe('checkAndUpdateWebhookData', () => {
    const mockWebhookData = {
      Id: 'test-record-id',
      FirstName: 'John',
      LastName: 'Doe',
      Email: '<EMAIL>',
      LastModifiedDate: new Date().toISOString()
    };

    it('should successfully process Contact webhook', async () => {
      mockPool.query.mockResolvedValue([
        { id: 1, sf_record_id: 'test-record-id', email: '<EMAIL>' }
      ]);

      webhookApiService.getContactDetails = jest.fn().mockResolvedValue({
        status: true,
        data: { contactId: 1 }
      });

      const result = await webhookApiService.checkAndUpdateWebhookData(
        mockWebhookData,
        'Contact',
        'update',
        '00DSv000000nGA5MAM',
        'test-session-id'
      );

      expect(result).toEqual({
        status: true,
        data: { contactId: 1 }
      });

      expect(mockEnhancedLogger.logWebhookProcessing).toHaveBeenCalledWith(
        'Contact',
        mockWebhookData,
        'processing_started',
        expect.objectContaining({
          organizationId: '00DSv000000nGA5MAM',
          sessionId: 'test-session-id'
        })
      );
    });

    it('should successfully process Customer_Details__c webhook', async () => {
      const customerWebhookData = {
        Id: 'test-customer-id',
        Name: 'Test Customer',
        LastModifiedDate: new Date().toISOString()
      };

      mockPool.query.mockResolvedValue([
        { id: 1, sf_record_id: 'test-customer-id' }
      ]);

      webhookApiService.getCustomerDetails = jest.fn().mockResolvedValue({
        status: true,
        data: { customerId: 1 }
      });

      const result = await webhookApiService.checkAndUpdateWebhookData(
        customerWebhookData,
        'Customer_Details__c',
        'update',
        '00DSv000000nGA5MAM',
        'test-session-id'
      );

      expect(result).toEqual({
        status: true,
        data: { customerId: 1 }
      });

      expect(webhookApiService.getCustomerDetails).toHaveBeenCalledWith(
        customerWebhookData,
        'test-session-id'
      );
    });

    it('should successfully process Service_Address__c webhook', async () => {
      const addressWebhookData = {
        Id: 'test-address-id',
        Name: 'Test Address',
        LastModifiedDate: new Date().toISOString()
      };

      mockPool.query.mockResolvedValue([
        { id: 1, sf_record_id: 'test-address-id' }
      ]);

      webhookApiService.getAddressDetails = jest.fn().mockResolvedValue({
        status: true,
        data: { addressId: 1 }
      });

      const result = await webhookApiService.checkAndUpdateWebhookData(
        addressWebhookData,
        'Service_Address__c',
        'update',
        '00DSv000000nGA5MAM',
        'test-session-id'
      );

      expect(result).toEqual({
        status: true,
        data: { addressId: 1 }
      });

      expect(webhookApiService.getAddressDetails).toHaveBeenCalledWith(
        addressWebhookData,
        'test-session-id'
      );
    });

    it('should handle unsupported webhook type', async () => {
      const unsupportedWebhookData = {
        Id: 'test-id',
        Name: 'Test Record'
      };

      const result = await webhookApiService.checkAndUpdateWebhookData(
        unsupportedWebhookData,
        'UnsupportedObject__c',
        'update',
        '00DSv000000nGA5MAM',
        'test-session-id'
      );

      expect(result).toEqual({
        status: false,
        message: 'Unsupported webhook type: UnsupportedObject__c'
      });

      expect(mockEnhancedLogger.warn).toHaveBeenCalledWith(
        'Unsupported webhook type received',
        expect.objectContaining({
          sfApiName: 'UnsupportedObject__c',
          recordId: 'test-id'
        }),
        'webhook_processing_logs'
      );
    });

    it('should handle database query errors', async () => {
      const error = new Error('Database connection failed');
      mockPool.query.mockRejectedValue(error);

      const result = await webhookApiService.checkAndUpdateWebhookData(
        mockWebhookData,
        'Contact',
        'update',
        '00DSv000000nGA5MAM',
        'test-session-id'
      );

      expect(result).toEqual({
        status: false,
        error: 'Database connection failed'
      });

      expect(mockEnhancedLogger.logWebhookError).toHaveBeenCalledWith(
        'Contact',
        error,
        mockWebhookData,
        expect.objectContaining({
          organizationId: '00DSv000000nGA5MAM',
          sessionId: 'test-session-id'
        })
      );
    });

    it('should handle no changes detected', async () => {
      hasValidChanges.mockReturnValue(false);
      
      mockPool.query.mockResolvedValue([
        { id: 1, sf_record_id: 'test-record-id', email: '<EMAIL>' }
      ]);

      const result = await webhookApiService.checkAndUpdateWebhookData(
        mockWebhookData,
        'Contact',
        'update',
        '00DSv000000nGA5MAM',
        'test-session-id'
      );

      expect(result).toEqual({
        status: true,
        message: 'No changes detected, skipping update'
      });

      expect(mockEnhancedLogger.info).toHaveBeenCalledWith(
        'No changes detected for webhook',
        expect.objectContaining({
          sfApiName: 'Contact',
          recordId: 'test-record-id'
        }),
        'webhook_processing_logs'
      );
    });
  });

  describe('getContactDetails', () => {
    it('should successfully update contact details', async () => {
      const contactData = {
        Id: 'test-contact-id',
        FirstName: 'John',
        LastName: 'Doe',
        Email: '<EMAIL>',
        Phone: '555-1234'
      };

      mockPool.query.mockResolvedValue({ affectedRows: 1 });

      const result = await webhookApiService.getContactDetails(contactData, 'test-session-id');

      expect(result).toEqual({
        status: true,
        message: 'Contact updated successfully',
        recordId: 'test-contact-id'
      });

      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE contacts SET'),
        expect.arrayContaining(['John', 'Doe', '<EMAIL>'])
      );

      expect(mockEnhancedLogger.logDatabaseOperation).toHaveBeenCalledWith(
        'UPDATE',
        'contacts',
        expect.objectContaining({
          recordId: 'test-contact-id',
          sessionId: 'test-session-id'
        })
      );
    });

    it('should handle contact update failure', async () => {
      const contactData = {
        Id: 'test-contact-id',
        FirstName: 'John',
        LastName: 'Doe',
        Email: '<EMAIL>'
      };

      mockPool.query.mockResolvedValue({ affectedRows: 0 });

      const result = await webhookApiService.getContactDetails(contactData, 'test-session-id');

      expect(result).toEqual({
        status: false,
        message: 'Contact update failed - no rows affected',
        recordId: 'test-contact-id'
      });
    });

    it('should handle database errors during contact update', async () => {
      const contactData = {
        Id: 'test-contact-id',
        FirstName: 'John',
        LastName: 'Doe',
        Email: '<EMAIL>'
      };

      const error = new Error('Database constraint violation');
      mockPool.query.mockRejectedValue(error);

      const result = await webhookApiService.getContactDetails(contactData, 'test-session-id');

      expect(result).toEqual({
        status: false,
        error: 'Database constraint violation',
        recordId: 'test-contact-id'
      });

      expect(mockEnhancedLogger.error).toHaveBeenCalledWith(
        'Contact update failed',
        expect.objectContaining({
          error: 'Database constraint violation',
          recordId: 'test-contact-id'
        }),
        'webhook_processing_logs'
      );
    });
  });

  describe('getCustomerDetails', () => {
    it('should successfully update customer details', async () => {
      const customerData = {
        Id: 'test-customer-id',
        Name: 'Test Customer',
        Email__c: '<EMAIL>',
        Phone__c: '555-5678'
      };

      mockPool.query.mockResolvedValue({ affectedRows: 1 });

      const result = await webhookApiService.getCustomerDetails(customerData, 'test-session-id');

      expect(result).toEqual({
        status: true,
        message: 'Customer details updated successfully',
        recordId: 'test-customer-id'
      });

      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE customer_details SET'),
        expect.arrayContaining(['Test Customer', '<EMAIL>'])
      );
    });

    it('should handle customer update failure', async () => {
      const customerData = {
        Id: 'test-customer-id',
        Name: 'Test Customer'
      };

      const error = new Error('Customer not found');
      mockPool.query.mockRejectedValue(error);

      const result = await webhookApiService.getCustomerDetails(customerData, 'test-session-id');

      expect(result).toEqual({
        status: false,
        error: 'Customer not found',
        recordId: 'test-customer-id'
      });
    });
  });

  describe('getAddressDetails', () => {
    it('should successfully update address details', async () => {
      const addressData = {
        Id: 'test-address-id',
        Name: 'Test Address',
        Street__c: '123 Main St',
        City__c: 'Test City',
        State__c: 'TS',
        Postal_Code__c: '12345'
      };

      mockPool.query.mockResolvedValue({ affectedRows: 1 });

      const result = await webhookApiService.getAddressDetails(addressData, 'test-session-id');

      expect(result).toEqual({
        status: true,
        message: 'Address details updated successfully',
        recordId: 'test-address-id'
      });

      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE service_addresses SET'),
        expect.arrayContaining(['Test Address', '123 Main St', 'Test City'])
      );
    });

    it('should handle address update failure', async () => {
      const addressData = {
        Id: 'test-address-id',
        Name: 'Test Address'
      };

      mockPool.query.mockResolvedValue({ affectedRows: 0 });

      const result = await webhookApiService.getAddressDetails(addressData, 'test-session-id');

      expect(result).toEqual({
        status: false,
        message: 'Address update failed - no rows affected',
        recordId: 'test-address-id'
      });
    });
  });

  describe('Performance and Logging', () => {
    it('should log performance metrics for webhook processing', async () => {
      const mockTimer = { end: jest.fn() };
      mockEnhancedLogger.createTimer.mockReturnValue(mockTimer);

      const webhookData = {
        Id: 'test-record-id',
        FirstName: 'John',
        LastName: 'Doe'
      };

      mockPool.query.mockResolvedValue([
        { id: 1, sf_record_id: 'test-record-id' }
      ]);

      webhookApiService.getContactDetails = jest.fn().mockResolvedValue({
        status: true,
        data: { contactId: 1 }
      });

      await webhookApiService.checkAndUpdateWebhookData(
        webhookData,
        'Contact',
        'update',
        '00DSv000000nGA5MAM',
        'test-session-id'
      );

      expect(mockEnhancedLogger.createTimer).toHaveBeenCalledWith('webhook_data_processing');
      expect(mockTimer.end).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        sfApiName: 'Contact',
        recordId: 'test-record-id'
      }));
    });
  });
});
