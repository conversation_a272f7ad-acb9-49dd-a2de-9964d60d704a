#!/bin/bash

# Enhanced Logging and Testing Setup Script
# This script sets up the enhanced logging system and test suite for all applications

set -e

echo "🚀 Setting up Enhanced Logging and Testing System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_status "Checking prerequisites..."

if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js 16+ and try again."
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed. Please install npm and try again."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    print_error "Node.js version 16+ is required. Current version: $(node --version)"
    exit 1
fi

print_success "Prerequisites check passed"

# Function to install dependencies for an application
install_app_dependencies() {
    local app_name=$1
    local app_path=$2
    
    print_status "Installing dependencies for $app_name..."
    
    if [ ! -d "$app_path" ]; then
        print_error "Directory $app_path does not exist"
        return 1
    fi
    
    cd "$app_path"
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found in $app_path"
        return 1
    fi
    
    # Install dependencies
    npm install
    
    if [ $? -eq 0 ]; then
        print_success "$app_name dependencies installed successfully"
    else
        print_error "Failed to install $app_name dependencies"
        return 1
    fi
    
    cd - > /dev/null
}

# Function to run tests for an application
run_app_tests() {
    local app_name=$1
    local app_path=$2
    
    print_status "Running tests for $app_name..."
    
    cd "$app_path"
    
    # Check if test script exists
    if ! npm run test --silent 2>/dev/null; then
        print_warning "No test script found for $app_name, skipping tests"
        cd - > /dev/null
        return 0
    fi
    
    # Run tests
    npm test
    
    if [ $? -eq 0 ]; then
        print_success "$app_name tests passed"
    else
        print_error "$app_name tests failed"
        cd - > /dev/null
        return 1
    fi
    
    cd - > /dev/null
}

# Function to setup logging directories
setup_logging_directories() {
    print_status "Setting up logging directories..."
    
    # Create log directories
    mkdir -p /tmp/salesforce-sync
    mkdir -p /tmp/webhook
    mkdir -p /tmp/node
    
    # Set permissions
    chmod 755 /tmp/salesforce-sync
    chmod 755 /tmp/webhook
    chmod 755 /tmp/node
    
    print_success "Logging directories created"
}

# Main installation process
main() {
    print_status "Starting Enhanced Logging and Testing Setup"
    
    # Setup logging directories
    setup_logging_directories
    
    # Install dependencies for each application
    print_status "Installing dependencies for all applications..."
    
    # Salesforce Sync
    if [ -d "salesforce-sync" ]; then
        install_app_dependencies "Salesforce Sync" "salesforce-sync"
    else
        print_warning "salesforce-sync directory not found, skipping"
    fi
    
    # Webhook
    if [ -d "webhook" ]; then
        install_app_dependencies "Webhook" "webhook"
    else
        print_warning "webhook directory not found, skipping"
    fi
    
    # Node Application
    if [ -d "node" ]; then
        install_app_dependencies "Node Application" "node"
    else
        print_warning "node directory not found, skipping"
    fi
    
    print_success "All dependencies installed successfully"
    
    # Run tests if requested
    if [ "$1" = "--with-tests" ] || [ "$1" = "-t" ]; then
        print_status "Running tests for all applications..."
        
        # Salesforce Sync Tests
        if [ -d "salesforce-sync" ]; then
            run_app_tests "Salesforce Sync" "salesforce-sync"
        fi
        
        # Webhook Tests
        if [ -d "webhook" ]; then
            run_app_tests "Webhook" "webhook"
        fi
        
        # Node Application Tests
        if [ -d "node" ]; then
            run_app_tests "Node Application" "node"
        fi
        
        print_success "All tests completed"
    fi
    
    # Display summary
    echo ""
    print_success "🎉 Enhanced Logging and Testing Setup Complete!"
    echo ""
    echo "📋 Summary:"
    echo "  ✅ Enhanced logging system configured"
    echo "  ✅ Test infrastructure setup"
    echo "  ✅ Dependencies installed"
    if [ "$1" = "--with-tests" ] || [ "$1" = "-t" ]; then
        echo "  ✅ Tests executed"
    fi
    echo ""
    echo "📖 Next Steps:"
    echo "  1. Review the LOGGING_AND_TESTING_GUIDE.md for detailed usage"
    echo "  2. Configure Elasticsearch connection if needed"
    echo "  3. Run individual tests: npm test"
    echo "  4. Run tests with coverage: npm run test:coverage"
    echo ""
    echo "🔧 Available Commands:"
    echo "  npm test                    # Run all tests"
    echo "  npm run test:watch         # Run tests in watch mode"
    echo "  npm run test:coverage      # Run tests with coverage"
    echo ""
}

# Handle script arguments
case "$1" in
    --help|-h)
        echo "Enhanced Logging and Testing Setup Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --with-tests, -t    Install dependencies and run tests"
        echo "  --help, -h          Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                  # Install dependencies only"
        echo "  $0 --with-tests     # Install dependencies and run tests"
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
