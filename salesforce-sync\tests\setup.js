// Global test setup for salesforce-sync
const path = require('path');

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_PREFIX = 'test';

// Mock console methods to reduce noise during tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  // Helper to create mock request objects
  createMockRequest: (overrides = {}) => ({
    method: 'GET',
    url: '/test',
    query: {},
    body: {},
    params: {},
    headers: {},
    get: jest.fn((header) => overrides.headers?.[header.toLowerCase()]),
    ip: '127.0.0.1',
    logData: {
      requestId: 'test-request-id',
      startTime: Date.now()
    },
    ...overrides
  }),

  // Helper to create mock response objects
  createMockResponse: (overrides = {}) => {
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      get: jest.fn(),
      locals: {},
      statusCode: 200,
      on: jest.fn(),
      ...overrides
    };
    return res;
  },

  // Helper to create mock next function
  createMockNext: () => jest.fn(),

  // Helper to wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to create mock database results
  createMockDbResult: (data = [], overrides = {}) => ({
    rows: data,
    rowCount: data.length,
    command: 'SELECT',
    ...overrides
  }),

  // Helper to create mock Salesforce data
  createMockSalesforceContact: (overrides = {}) => ({
    Id: 'test-sf-id-123',
    FirstName: 'Test',
    LastName: 'User',
    Email: '<EMAIL>',
    Phone: '555-1234',
    LastModifiedDate: new Date().toISOString(),
    CreatedDate: new Date().toISOString(),
    ...overrides
  }),

  // Helper to create mock Elasticsearch response
  createMockElasticResponse: (overrides = {}) => ({
    _id: 'test-elastic-id',
    _index: 'test-index',
    _version: 1,
    result: 'created',
    ...overrides
  })
};

// Global beforeEach to reset all mocks
beforeEach(() => {
  jest.clearAllMocks();
});

// Global afterEach cleanup
afterEach(() => {
  jest.restoreAllMocks();
});

// Handle unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);
