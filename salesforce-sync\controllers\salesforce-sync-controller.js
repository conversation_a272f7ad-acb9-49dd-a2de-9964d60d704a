
const DeleteContactService = require("../services/delete-contact-service");
const ContactTestService = require("../services/process-services/contact-sync-services");
const OrderTestServices = require("../services/process-services/order-services");
const EnhancedLogger = require("../utils/enhanced-logger");

/**
 * Controller method to sync customer details from Salesforce.
 * @param {object} req - Express request object.
 * @param {object} res - Express response object.
 * @param {function} next - Express next function.
 */
class SalesforceSyncController {
    constructor() {
        this.logger = new EnhancedLogger();
    }

    async deleteContact(req, res, next) {
        const timer = this.logger.createTimer('delete_contact_operation');

        try {
            const { email } = req.body;

            await this.logger.info('Delete contact request received', {
                email,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            if (!email) {
                await this.logger.warn('Delete contact failed: Email not provided', {
                    requestId: req.logData?.requestId
                }, 'api_operations_logs');
                throw new Error("Email not found");
            }

            const deleteContactService = new DeleteContactService();
            const result = await deleteContactService.deleteContact(email);

            await timer.end({
                success: true,
                email,
                requestId: req.logData?.requestId
            });

            await this.logger.info('Delete contact completed successfully', {
                email,
                result,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            res.status(200).send({ message: `Success`, email });
        } catch (error) {
            await timer.end({
                success: false,
                error: error.message,
                requestId: req.logData?.requestId
            });

            await this.logger.error('Delete contact failed', {
                error: error.message,
                stack: error.stack,
                email: req.body?.email,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            next(error);
        }
    }

    async getContactsAndSync(req, res, next) {
        const timer = this.logger.createTimer('contacts_sync_operation');

        try {
            const { email } = req.query;

            await this.logger.info('Contacts sync request received', {
                email,
                syncType: email ? 'single_contact' : 'all_contacts',
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            const contactTestService = new ContactTestService();
            const result = await contactTestService.getContactDetails(email);

            await timer.end({
                success: result?.status || false,
                contactsProcessed: result?.contactCount || 0,
                email,
                requestId: req.logData?.requestId
            });

            await this.logger.info('Contacts sync completed', {
                result,
                email,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            res.status(200).send({
                message: `Success`,
                contactsProcessed: result?.contactCount || 0,
                status: result?.status || false
            });
        } catch (error) {
            await timer.end({
                success: false,
                error: error.message,
                requestId: req.logData?.requestId
            });

            await this.logger.error('Contacts sync failed', {
                error: error.message,
                stack: error.stack,
                email: req.query?.email,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            next(error);
        }
    }

    async getOrderssAndSync(req, res, next) {
        const timer = this.logger.createTimer('orders_sync_operation');

        try {
            await this.logger.info('Orders sync request received', {
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            const orderTestServices = new OrderTestServices();
            const result = await orderTestServices.getCreationOrderDetails();

            await timer.end({
                success: result?.status || false,
                ordersProcessed: result?.orderCount || 0,
                requestId: req.logData?.requestId
            });

            await this.logger.info('Orders sync completed', {
                result,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            res.status(200).send({
                message: `Success`,
                ordersProcessed: result?.orderCount || 0,
                status: result?.status || false
            });
        } catch (error) {
            await timer.end({
                success: false,
                error: error.message,
                requestId: req.logData?.requestId
            });

            await this.logger.error('Orders sync failed', {
                error: error.message,
                stack: error.stack,
                requestId: req.logData?.requestId
            }, 'api_operations_logs');

            next(error);
        }
    }
}
// Export an instance of the SalesforceSyncController class
module.exports = new SalesforceSyncController();