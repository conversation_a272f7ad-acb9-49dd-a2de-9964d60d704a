// Centralized mock exports for webhook tests

// Database mocks
const mockPool = {
  query: jest.fn(),
  end: jest.fn(),
  connect: jest.fn(),
  release: jest.fn()
};

// Salesforce client mock
const mockSalesforceClient = {
  login: jest.fn(),
  query: jest.fn(),
  sobject: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  fetchAllRecords: jest.fn(),
  getDeletedData: jest.fn(),
  getAddressDetailsById: jest.fn(),
  getDetailsByFieldAndApi: jest.fn()
};

// Elasticsearch client mock
const mockElasticsearchClient = {
  index: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  search: jest.fn(),
  indices: {
    create: jest.fn(),
    exists: jest.fn(),
    delete: jest.fn()
  }
};

// Enhanced logger mock
const mockEnhancedLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  logWebhookReceived: jest.fn(),
  logWebhookProcessing: jest.fn(),
  logWebhookError: jest.fn(),
  logDataTransformation: jest.fn(),
  logDatabaseOperation: jest.fn(),
  logSalesforceOperation: jest.fn(),
  logXmlParsing: jest.fn(),
  logAuthentication: jest.fn(),
  createTimer: jest.fn(() => ({
    end: jest.fn()
  }))
};

// XML2JS mock
const mockXml2js = {
  parseString: jest.fn(),
  Builder: jest.fn(() => ({
    buildObject: jest.fn()
  }))
};

// Body parser XML mock
const mockBodyParserXml = jest.fn();

// Axios mock
const mockAxios = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  create: jest.fn(() => mockAxios)
};

// Express mock
const mockExpress = {
  Router: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    use: jest.fn()
  })),
  json: jest.fn(),
  urlencoded: jest.fn(),
  static: jest.fn()
};

// Mock factory functions
const createMockWebhookService = (methods = {}) => {
  const defaultMethods = {
    checkAndUpdateWebhookData: jest.fn(),
    getCustomerDetails: jest.fn(),
    getContactDetails: jest.fn(),
    getAddressDetails: jest.fn(),
    getInternetDetails: jest.fn(),
    getTVDetails: jest.fn(),
    getPhoneDetails: jest.fn()
  };
  
  return {
    ...defaultMethods,
    ...methods
  };
};

const createMockWebhookController = (methods = {}) => {
  const defaultMethods = {
    updateWebhook: jest.fn()
  };
  
  return {
    ...defaultMethods,
    ...methods
  };
};

// Mock helper functions
const mockHelpers = {
  decryptXML: jest.fn(),
  getCurrentAtlanticTime: jest.fn(() => new Date().toISOString()),
  generateSfAdrsObj: jest.fn(),
  hasValidChanges: jest.fn(),
  sanitizeValue: jest.fn()
};

// Mock responses
const mockResponses = {
  webhookData: {
    Id: 'test-record-id',
    Name: 'Test Record',
    LastModifiedDate: new Date().toISOString(),
    CreatedDate: new Date().toISOString()
  },
  
  decryptedXml: {
    status: true,
    webhookData: {
      Id: 'test-record-id',
      Name: 'Test Record'
    },
    sfApiName: 'Contact',
    organizationId: '00DSv000000nGA5MAM',
    sessionId: 'test-session-id'
  },
  
  elasticResponse: {
    _id: 'test-elastic-id',
    _index: 'test-index',
    _version: 1,
    result: 'created'
  },
  
  dbQueryResult: {
    rows: [],
    rowCount: 0,
    command: 'SELECT'
  },
  
  soapResponse: `
    <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
      <soap:Body>
        <response>
          <status>true</status>
        </response>
      </soap:Body>
    </soap:Envelope>
  `
};

// Sample XML data for testing
const sampleXmlData = {
  contact: `<?xml version="1.0" encoding="UTF-8"?>
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
      <soapenv:Body>
        <notifications xmlns="http://soap.sforce.com/2005/09/outbound">
          <OrganizationId>00DSv000000nGA5MAM</OrganizationId>
          <ActionId>04kSv0000004SlhIAE</ActionId>
          <SessionId>test-session-id</SessionId>
          <Notification>
            <Id>test-notification-id</Id>
            <sObject xsi:type="sf:Contact" xmlns:sf="urn:sobject.enterprise.soap.sforce.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <sf:Id>test-contact-id</sf:Id>
              <sf:FirstName>Test</sf:FirstName>
              <sf:LastName>User</sf:LastName>
              <sf:Email><EMAIL></sf:Email>
            </sObject>
          </Notification>
        </notifications>
      </soapenv:Body>
    </soapenv:Envelope>`,
  
  customerDetails: `<?xml version="1.0" encoding="UTF-8"?>
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
      <soapenv:Body>
        <notifications xmlns="http://soap.sforce.com/2005/09/outbound">
          <OrganizationId>00DSv000000nGA5MAM</OrganizationId>
          <Notification>
            <sObject xsi:type="sf:Customer_Details__c" xmlns:sf="urn:sobject.enterprise.soap.sforce.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <sf:Id>test-customer-id</sf:Id>
              <sf:Name>Test Customer</sf:Name>
            </sObject>
          </Notification>
        </notifications>
      </soapenv:Body>
    </soapenv:Envelope>`
};

module.exports = {
  mockPool,
  mockSalesforceClient,
  mockElasticsearchClient,
  mockEnhancedLogger,
  mockXml2js,
  mockBodyParserXml,
  mockAxios,
  mockExpress,
  createMockWebhookService,
  createMockWebhookController,
  mockHelpers,
  mockResponses,
  sampleXmlData
};
