const request = require('supertest');
const express = require('express');
const ChargebeeSyncController = require('../../controllers/chargebee-sync-controller');
const { mockEnhancedLogger, createMockService } = require('../mocks');

// Mock dependencies
jest.mock('../../utils/enhanced-logger');
jest.mock('../../services/chargebee/invoice-sync-services');
jest.mock('../../services/cron-services');

const InvoicesSyncService = require('../../services/chargebee/invoice-sync-services');
const CronService = require('../../services/cron-services');

describe('ChargebeeSyncController', () => {
  let app;
  let mockInvoiceService;
  let mockCronService;

  beforeEach(() => {
    // Setup Express app for testing
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Add mock request logging data
    app.use((req, res, next) => {
      req.logData = { requestId: 'test-request-id', startTime: Date.now() };
      next();
    });

    // Setup routes
    app.get('/sync-invoices', ChargebeeSyncController.syncInvoicesFromChargebee.bind(ChargebeeSyncController));
    app.post('/remove-services', ChargebeeSyncController.removeServicesFromDatabase.bind(ChargebeeSyncController));

    // Setup service mocks
    mockInvoiceService = createMockService({
      getInvoicesList: jest.fn()
    });
    mockCronService = createMockService({
      updateSpeedchange: jest.fn()
    });

    InvoicesSyncService.mockImplementation(() => mockInvoiceService);
    CronService.mockImplementation(() => mockCronService);
  });

  describe('GET /sync-invoices', () => {
    it('should successfully sync invoices from Chargebee', async () => {
      const invoiceResult = {
        invoiceCount: 25,
        status: 'success'
      };
      mockInvoiceService.getInvoicesList.mockResolvedValue(invoiceResult);

      const response = await request(app)
        .get('/sync-invoices')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        invoicesProcessed: 25
      });
      expect(mockInvoiceService.getInvoicesList).toHaveBeenCalled();
    });

    it('should handle invoice sync with no results', async () => {
      const invoiceResult = {
        invoiceCount: 0,
        status: 'success'
      };
      mockInvoiceService.getInvoicesList.mockResolvedValue(invoiceResult);

      const response = await request(app)
        .get('/sync-invoices')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        invoicesProcessed: 0
      });
    });

    it('should handle service errors gracefully', async () => {
      const error = new Error('Chargebee API error');
      mockInvoiceService.getInvoicesList.mockRejectedValue(error);

      await request(app)
        .get('/sync-invoices')
        .expect(500);

      expect(mockInvoiceService.getInvoicesList).toHaveBeenCalled();
    });

    it('should log invoice sync operations correctly', async () => {
      const invoiceResult = { invoiceCount: 10 };
      mockInvoiceService.getInvoicesList.mockResolvedValue(invoiceResult);

      await request(app)
        .get('/sync-invoices')
        .expect(200);

      expect(mockEnhancedLogger.prototype.info).toHaveBeenCalledWith(
        'Chargebee invoice sync request received',
        expect.objectContaining({ requestId: 'test-request-id' }),
        'api_operations_logs'
      );

      expect(mockEnhancedLogger.prototype.info).toHaveBeenCalledWith(
        'Chargebee invoice sync completed',
        expect.objectContaining({ 
          result: invoiceResult,
          requestId: 'test-request-id'
        }),
        'api_operations_logs'
      );
    });

    it('should create and end performance timers', async () => {
      const mockTimer = { end: jest.fn() };
      mockEnhancedLogger.prototype.createTimer.mockReturnValue(mockTimer);
      
      mockInvoiceService.getInvoicesList.mockResolvedValue({ invoiceCount: 5 });

      await request(app)
        .get('/sync-invoices')
        .expect(200);

      expect(mockEnhancedLogger.prototype.createTimer).toHaveBeenCalledWith('chargebee_invoice_sync_operation');
      expect(mockTimer.end).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        invoicesProcessed: 5
      }));
    });
  });

  describe('POST /remove-services', () => {
    it('should successfully remove services from database', async () => {
      const removeResult = {
        removedCount: 15,
        status: 'success'
      };
      mockCronService.updateSpeedchange.mockResolvedValue(removeResult);

      const response = await request(app)
        .post('/remove-services')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        servicesRemoved: 15
      });
      expect(mockCronService.updateSpeedchange).toHaveBeenCalled();
    });

    it('should handle remove services with no results', async () => {
      const removeResult = {
        removedCount: 0,
        status: 'success'
      };
      mockCronService.updateSpeedchange.mockResolvedValue(removeResult);

      const response = await request(app)
        .post('/remove-services')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Success',
        servicesRemoved: 0
      });
    });

    it('should handle service errors gracefully', async () => {
      const error = new Error('Database error');
      mockCronService.updateSpeedchange.mockRejectedValue(error);

      await request(app)
        .post('/remove-services')
        .expect(500);

      expect(mockCronService.updateSpeedchange).toHaveBeenCalled();
    });

    it('should log remove services operations correctly', async () => {
      const removeResult = { removedCount: 8 };
      mockCronService.updateSpeedchange.mockResolvedValue(removeResult);

      await request(app)
        .post('/remove-services')
        .expect(200);

      expect(mockEnhancedLogger.prototype.info).toHaveBeenCalledWith(
        'Remove services request received',
        expect.objectContaining({ requestId: 'test-request-id' }),
        'api_operations_logs'
      );

      expect(mockEnhancedLogger.prototype.info).toHaveBeenCalledWith(
        'Remove services completed',
        expect.objectContaining({ 
          result: removeResult,
          requestId: 'test-request-id'
        }),
        'api_operations_logs'
      );
    });

    it('should create and end performance timers', async () => {
      const mockTimer = { end: jest.fn() };
      mockEnhancedLogger.prototype.createTimer.mockReturnValue(mockTimer);
      
      mockCronService.updateSpeedchange.mockResolvedValue({ removedCount: 3 });

      await request(app)
        .post('/remove-services')
        .expect(200);

      expect(mockEnhancedLogger.prototype.createTimer).toHaveBeenCalledWith('remove_services_operation');
      expect(mockTimer.end).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        servicesRemoved: 3
      }));
    });
  });

  describe('Error Handling and Logging', () => {
    it('should log errors with proper context for invoice sync', async () => {
      const error = new Error('Test error');
      mockInvoiceService.getInvoicesList.mockRejectedValue(error);

      await request(app)
        .get('/sync-invoices')
        .expect(500);

      expect(mockEnhancedLogger.prototype.error).toHaveBeenCalledWith(
        'Chargebee invoice sync failed',
        expect.objectContaining({
          error: 'Test error',
          stack: expect.any(String),
          requestId: 'test-request-id'
        }),
        'api_operations_logs'
      );
    });

    it('should log errors with proper context for remove services', async () => {
      const error = new Error('Database connection failed');
      mockCronService.updateSpeedchange.mockRejectedValue(error);

      await request(app)
        .post('/remove-services')
        .expect(500);

      expect(mockEnhancedLogger.prototype.error).toHaveBeenCalledWith(
        'Remove services failed',
        expect.objectContaining({
          error: 'Database connection failed',
          stack: expect.any(String),
          requestId: 'test-request-id'
        }),
        'api_operations_logs'
      );
    });

    it('should end timers even when errors occur', async () => {
      const mockTimer = { end: jest.fn() };
      mockEnhancedLogger.prototype.createTimer.mockReturnValue(mockTimer);
      
      const error = new Error('Test error');
      mockInvoiceService.getInvoicesList.mockRejectedValue(error);

      await request(app)
        .get('/sync-invoices')
        .expect(500);

      expect(mockTimer.end).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        error: 'Test error'
      }));
    });
  });
});
