const express = require("express");
const cors = require('cors');
const CONFIG = require("./config");
const { exceptionHandling } = require('./helper/exceptionHandling');
const { getCurrentAtlanticTime } = require("./helper/custom-helper");
const LoggingMiddleware = require('./middleware/logging-middleware');
const EnhancedLogger = require('./utils/enhanced-logger');
require("./db")

// Define server port
const PORT = CONFIG.NODE_ENV == "local" ? CONFIG.PORT : 4000;
const ALLOWED_IP = CONFIG.ALLOWED_IP;

const app = express();
const loggingMiddleware = new LoggingMiddleware();
const logger = new EnhancedLogger();

// Start cron jobs
require("./cron/cron")()

// Middleware to parse the request body as JSON
app.use(express.json());

// Middleware to parse the request body as URL-encoded data
app.use(express.urlencoded({ extended: true, limit: 'mb' }));

// Middleware to enable Cross-Origin Resource Sharing (CORS)
app.use(cors());

// Add logging middleware
app.use(loggingMiddleware.requestLogger());
app.use(loggingMiddleware.responseLogger());

// Middleware to define a route for the root URL
app.get('/', (req, res) => {
    res.status(200).send({ message: `SYNC PROCESS RUNNING ON PORT ${PORT}` });
});

// Load the routes
require("./routes/sync-route")(app);

// Add error logging middleware
app.use(loggingMiddleware.errorLogger());
app.use(exceptionHandling);

// Start server
app.listen(PORT, async () => {
    const startupMessage = `Salesforce Sync Service started on port ${PORT}`;

    // Log to both Winston and Elasticsearch
    await logger.info(startupMessage, {
        port: PORT,
        environment: CONFIG.NODE_ENV,
        timestamp: getCurrentAtlanticTime()
    }, 'application_startup_logs');

    // Keep console log for immediate visibility
    console.log({
        "log.level": "INFO",
        message: startupMessage,
        timestamp: getCurrentAtlanticTime()
    });
});